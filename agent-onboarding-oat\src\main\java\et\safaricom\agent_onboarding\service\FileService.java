package et.safaricom.agent_onboarding.service;

import com.mpesautils.logmanager.Log;
import com.mpesautils.logmanager.LogManager;
import et.safaricom.agent_onboarding.dto.request.UploadFileByBase46RequestDto;
import et.safaricom.agent_onboarding.enums.FILE_GROUP;
import et.safaricom.agent_onboarding.enums.REQUIRED_FILE_TYPE;
import et.safaricom.agent_onboarding.utils.LoggerCommon;
import et.safaricom.agent_onboarding.utils.ResponseTemplate;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Base64;
import java.util.List;
import java.util.UUID;

@Service
@RequiredArgsConstructor
public class FileService {
    private final LoggerCommon loggerCommon;

    @Value("${ba-onboarding.file-upload.dir}")
    private String uploadDir;

    public ResponseTemplate<String> saveFile(FILE_GROUP fileGroup, Long id, MultipartFile file, LogManager logManager) {
        String transactionId = "ONE_PLATFORM_BA_ONBOARDING_" + System.currentTimeMillis();

        Log log = new Log(transactionId, "info", "Uploading file", "", "", "", "ONE_PLATFORM_BA_ONBOARDING", "ONE_PLATFORM_BA_ONBOARDING",
                "0", "Default!", "Default Detailed Message.", "");
        try {
            String fileGroupStr = fileGroup.name().toLowerCase();
            Path directoryPath = Paths.get(uploadDir, String.valueOf(id), fileGroupStr);
            Files.createDirectories(directoryPath);

            String originalFileName = file.getOriginalFilename();
            String fileExtension = "";
            if (originalFileName.contains(".")) {
                fileExtension = originalFileName.substring(originalFileName.lastIndexOf("."));
            }

            String newFileName = "BA_ONBOARDING_" + UUID.randomUUID() + fileExtension;
            Path filePath = directoryPath.resolve(newFileName);

            Files.write(filePath, file.getBytes());
            String relativePath = id + "/" + fileGroupStr + "/" + newFileName;

            logManager.info(loggerCommon.getLog(log, "info", 0, "Success!", "File saved to: [ " + relativePath + " ]"));
            return ResponseTemplate.success(relativePath);
        } catch (Exception e) {
            e.printStackTrace();
            logManager.error(loggerCommon.getLog(log, 1, "Something went wrong! Please try again.", e));
            return ResponseTemplate.error("Something went wrong! Please try again.");
        }
    }

    public ResponseTemplate<String> saveFileFromBase64(UploadFileByBase46RequestDto uploadFileByBase46RequestDto, LogManager logManager) {
        String transactionId = "ONE_PLATFORM_BA_ONBOARDING_" + System.currentTimeMillis();

        Log log = new Log(transactionId, "info", "Uploading file (base64)", "", "", "", "ONE_PLATFORM_BA_ONBOARDING", "ONE_PLATFORM_BA_ONBOARDING",
                "0", "Default!", "Default Detailed Message.", "");

        try {
            Long id = uploadFileByBase46RequestDto.getId();
            FILE_GROUP fileGroup = uploadFileByBase46RequestDto.getFileGroup();
            String base64String = uploadFileByBase46RequestDto.getFileBase64();

            if (base64String == null || base64String.isEmpty()) {
                return ResponseTemplate.error("File data is empty!");
            }

            String fileExtension = ".png";
            if (base64String.startsWith("data:image/jpeg")) {
                fileExtension = ".jpg";
            } else if (base64String.startsWith("data:image/png")) {
                fileExtension = ".png";
            } else if (base64String.startsWith("data:application/pdf")) {
                fileExtension = ".pdf";
            }

            String base64Data = base64String.contains(",") ? base64String.split(",")[1] : base64String;

            byte[] fileBytes = Base64.getDecoder().decode(base64Data);
            String fileGroupStr = fileGroup.name().toLowerCase();
            Path directoryPath = Paths.get(uploadDir, String.valueOf(id), fileGroupStr);
            Files.createDirectories(directoryPath);

            String newFileName = "BA_ONBOARDING_" + UUID.randomUUID() + fileExtension;
            Path filePath = directoryPath.resolve(newFileName);

            Files.write(filePath, fileBytes);

            String relativePath = id + "/" + fileGroupStr + "/" + newFileName;

            logManager.info(loggerCommon.getLog(log, "info", 0, "Success!", "File saved to: [ " + relativePath + " ]"));
            return ResponseTemplate.success(relativePath);
        } catch (Exception e) {
            e.printStackTrace();
            logManager.error(loggerCommon.getLog(log, 1, "Something went wrong! Please try again.", e));
            return ResponseTemplate.error("Something went wrong! Please try again.");
        }
    }

    public ResponseEntity<?> getFile(String filePath, REQUIRED_FILE_TYPE requiredFileType, LogManager logManager) {
        String transactionId = "ONE_PLATFORM_BA_ONBOARDING_" + System.currentTimeMillis();

        Log log = new Log(transactionId, "info", "Downloading file", "", "", "", "ONE_PLATFORM_BA_ONBOARDING", "ONE_PLATFORM_BA_ONBOARDING",
                "0", "Default!", "Default Detailed Message.", "");
        try {
            Path fullPath = Paths.get(uploadDir, filePath);

            if (!Files.exists(fullPath)) {
                logManager.error(loggerCommon.getLog(log, "error", 1, "File not found!", "File [ " + fullPath + " ] not found!"));
                return ResponseEntity.status(HttpStatus.NOT_FOUND).body("File not found!");
            }

            byte[] fileBytes = Files.readAllBytes(fullPath);

            if (requiredFileType == REQUIRED_FILE_TYPE.BASE64) {
                String base64Encoded = Base64.getEncoder().encodeToString(fileBytes);
                return ResponseEntity.ok(base64Encoded);
            } else {
                String contentType = Files.probeContentType(fullPath);
                return ResponseEntity.ok()
                        .contentType(MediaType.parseMediaType(contentType))
                        .body(fileBytes);
            }
        } catch (Exception e) {
            e.printStackTrace();
            logManager.error(loggerCommon.getLog(log, 1, "Something went wrong! Please try again.", e));
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("Something went wrong! Please try again.");
        }
    }

    public void deleteFilesByPaths(List<String> imagePaths, LogManager logManager, Log log) {
        for (String path : imagePaths) {
            try {
                Path fullPath = Paths.get(uploadDir, path);
                Files.deleteIfExists(fullPath);

                logManager.info(loggerCommon.getLog(log, "info", 0, "Success!", "File [ " + path + " ] removed"));
            } catch (IOException e) {
                e.printStackTrace();
                logManager.error(loggerCommon.getLog(log, "error", 1, "Something went wrong! Please try again.", "Failed to delete file: " + path + " | Reason: " + e.getMessage()));
            }
        }
    }

}
