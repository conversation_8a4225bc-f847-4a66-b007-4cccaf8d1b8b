package et.safaricom.agent_onboarding.dto.response.api;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

@Data
@ToString
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class EKycVerificationIdApiResponseDto {
    @JsonProperty("status")
    private Boolean status;

    @JsonProperty("ekycId")
    private String ekycId;

    @JsonProperty("score")
    private String score;

}
