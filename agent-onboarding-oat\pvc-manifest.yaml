apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: channels-agent-onboarding-oat-claim
  namespace: mpesa-channels
  labels:
    app: agent-onboarding
    component: storage
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 10Gi
  storageClassName: vsphere-with-tanzu-storage-policy
  volumeMode: Filesystem
---
# Optional: If you need ReadWriteMany access (multiple pods accessing same volume)
# apiVersion: v1
# kind: PersistentVolumeClaim
# metadata:
#   name: channels-agent-onboarding-oat-claim
#   namespace: mpesa-channels
#   labels:
#     app: agent-onboarding
#     component: storage
# spec:
#   accessModes:
#     - ReadWriteMany
#   resources:
#     requests:
#       storage: 10Gi
#   storageClassName: vsphere-with-tanzu-storage-policy
#   volumeMode: Filesystem
