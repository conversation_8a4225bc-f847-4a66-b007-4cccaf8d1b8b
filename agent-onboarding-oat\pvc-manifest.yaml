apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: channels-agent-onboarding-prod-claim-v2
  namespace: mpesa-channels
  labels:
    app: agent-onboarding
    component: storage
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 10Gi
  storageClassName: standard  # Change this to your cluster's storage class
---
# Optional: If you need ReadWriteMany access (multiple pods accessing same volume)
# apiVersion: v1
# kind: PersistentVolumeClaim
# metadata:
#   name: channels-agent-onboarding-prod-claim-v2
#   namespace: mpesa-channels
#   labels:
#     app: agent-onboarding
#     component: storage
# spec:
#   accessModes:
#     - ReadWriteMany
#   resources:
#     requests:
#       storage: 10Gi
#   storageClassName: nfs-client  # Use appropriate storage class for RWX
