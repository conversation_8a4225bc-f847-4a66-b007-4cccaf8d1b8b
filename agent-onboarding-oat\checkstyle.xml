<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE module PUBLIC
        "-//Checkstyle//DTD Checkstyle Configuration 1.3//EN"
        "https://checkstyle.org/dtds/configuration_1_3.dtd">

<module name="Checker">

    <!-- General settings -->
    <module name="FileTabCharacter">
        <property name="eachLine" value="true"/>
    </module>

    <module name="LineLength">
        <property name="max" value="120"/>
        <property name="ignorePattern" value="^package.*|^import.*"/>
    </module>

    <module name="TreeWalker">

        <!-- Class & Interface names: PascalCase -->
        <module name="TypeName">
            <property name="format" value="^[A-Z][a-zA-Z0-9]*$"/>
        </module>

        <!-- Enum names: PascalCase, Enum values: UPPER_SNAKE_CASE -->
        <module name="EnumName"/>
        <module name="EnumConstantName"/>

        <!-- Method names: camelCase -->
        <module name="MethodName">
            <property name="format" value="^[a-z][a-zA-Z0-9]*$"/>
        </module>

        <!-- Variable names: camelCase -->
        <module name="LocalVariableName">
            <property name="format" value="^[a-z][a-zA-Z0-9]*$"/>
        </module>

        <!-- Constants: UPPER_SNAKE_CASE -->
        <module name="ConstantName">
            <property name="format" value="^[A-Z][A-Z0-9_]*$"/>
        </module>

        <!-- Indentation -->
        <module name="Indentation">
            <property name="basicOffset" value="4"/>
            <property name="braceAdjustment" value="0"/>
            <property name="caseIndent" value="4"/>
            <property name="lineWrappingIndentation" value="8"/>
        </module>

        <!-- Braces style: always use braces -->
        <module name="NeedBraces"/>

        <!-- Whitespace checks -->
        <module name="WhitespaceAfter"/>
        <module name="WhitespaceAround"/>
        <module name="EmptyLineSeparator"/>

    </module>
</module>
