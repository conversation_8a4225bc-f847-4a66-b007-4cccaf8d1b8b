package et.safaricom.agent_onboarding.model.view.snd;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import lombok.Getter;
import lombok.ToString;
import org.hibernate.annotations.Immutable;
import org.hibernate.annotations.Subselect;

import java.time.LocalDateTime;

@Getter
@ToString
@Entity
@Immutable
@JsonIgnoreProperties(ignoreUnknown = true)
@Subselect("""
    SELECT sales_channel_id, channel_type_id, channel_sub_type_id, user_id, parent_id, pan_no, tan_no, registration_status, credit_limit, sales_channel_name, dealer_id, category,
           channel_category, remarks, kuchio_id, add_by, add_date, mod_by, mod_date, approved_by, approval_date, add_by_user_type, send_to_pos, min_order_value, start_date, end_date,
           crdt_limit, category_id, document_type, phone_no, fax_no, dist_class, "location", email_address, iso_flag, social_reason, source_warehouse_id, send_to_erp, new_owner,
           parent_change_reason, parent_change_date, status_change_date, status_change_reason, parent_change_remark, root_parent_id, sales_channel_level, virtual_warehouse,
           account_manager, send_to_bi, name_prefix, status_changed_by, business_license_field_no, commercial_registration_field, nationality, parent1, parent2, parent3,
           business_till_no, organization_short_code, pretups_flag, erp_flag, ekyc_flag, agent_till_no, visitor_plan, multiple_currency, send_to_pretups, shift_flag
    FROM onboardings.snd_sales_channel_master
""")
public class SalesChannelMaster {
    @Id
    @Column(name = "sales_channel_id")
    private Long salesChannelId;

    @Column(name = "channel_type_id")
    private Double channelTypeId;

    @Column(name = "channel_sub_type_id")
    private Double channelSubTypeId;

    @Column(name = "user_id")
    private String userId;

    @Column(name = "parent_id")
    private Double parentId;

    @Column(name = "pan_no")
    private String panNo;

    @Column(name = "tan_no")
    private String tanNo;

    @Column(name = "registration_status")
    private Double registrationStatus;

    @Column(name = "credit_limit")
    private Double creditLimit;

    @Column(name = "sales_channel_name")
    private String salesChannelName;

    @Column(name = "dealer_id")
    private String dealerId;

    @Column(name = "category")
    private String category;

    @Column(name = "channel_category")
    private String channelCategory;

    @Column(name = "remarks")
    private String remarks;

    @Column(name = "kuchio_id")
    private String kuchioId;

    @Column(name = "add_by")
    private String addBy;

    @Column(name = "add_date")
    private LocalDateTime addDate;

    @Column(name = "mod_by")
    private String modBy;

    @Column(name = "mod_date")
    private LocalDateTime modDate;

    @Column(name = "approved_by")
    private String approvedBy;

    @Column(name = "approval_date")
    private LocalDateTime approvalDate;

    @Column(name = "add_by_user_type")
    private Double addByUserType;

    @Column(name = "send_to_pos")
    private String sendToPos;

    @Column(name = "min_order_value")
    private Double minOrderValue;

    @Column(name = "start_date")
    private LocalDateTime startDate;

    @Column(name = "end_date")
    private LocalDateTime endDate;

    @Column(name = "crdt_limit")
    private Double crdtLimit;

    @Column(name = "category_id")
    private Double categoryId;

    @Column(name = "document_type")
    private String documentType;

    @Column(name = "phone_no")
    private String phoneNo;

    @Column(name = "fax_no")
    private String faxNo;

    @Column(name = "dist_class")
    private String distClass;

    @Column(name = "\"location\"")
    private String location;

    @Column(name = "email_address")
    private String emailAddress;

    @Column(name = "iso_flag")
    private String isoFlag;

    @Column(name = "social_reason")
    private String socialReason;

    @Column(name = "source_warehouse_id")
    private Integer sourceWarehouseId;

    @Column(name = "send_to_erp")
    private String sendToErp;

    @Column(name = "new_owner")
    private Integer newOwner;

    @Column(name = "parent_change_reason")
    private Integer parentChangeReason;

    @Column(name = "parent_change_date")
    private LocalDateTime parentChangeDate;

    @Column(name = "status_change_date")
    private LocalDateTime statusChangeDate;

    @Column(name = "status_change_reason")
    private String statusChangeReason;

    @Column(name = "parent_change_remark")
    private String parentChangeRemark;

    @Column(name = "root_parent_id")
    private Integer rootParentId;

    @Column(name = "sales_channel_level")
    private Integer salesChannelLevel;

    @Column(name = "virtual_warehouse")
    private Integer virtualWarehouse;

    @Column(name = "account_manager")
    private String accountManager;

    @Column(name = "send_to_bi")
    private String sendToBi;

    @Column(name = "name_prefix")
    private String namePrefix;

    @Column(name = "status_changed_by")
    private String statusChangedBy;

    @Column(name = "business_license_field_no")
    private String businessLicenseFieldNo;

    @Column(name = "commercial_registration_field")
    private String commercialRegistrationField;

    @Column(name = "nationality")
    private String nationality;

    @Column(name = "parent1")
    private Integer parent1;

    @Column(name = "parent2")
    private Integer parent2;

    @Column(name = "parent3")
    private Integer parent3;

    @Column(name = "business_till_no")
    private String businessTillNo;

    @Column(name = "organization_short_code")
    private String organizationShortCode;

    @Column(name = "pretups_flag")
    private String pretupsFlag;

    @Column(name = "erp_flag")
    private String erpFlag;

    @Column(name = "ekyc_flag")
    private String ekycFlag;

    @Column(name = "agent_till_no")
    private String agentTillNo;

    @Column(name = "visitor_plan")
    private String visitorPlan;

    @Column(name = "multiple_currency")
    private String multipleCurrency;

    @Column(name = "send_to_pretups")
    private String sendToPretups;

    @Column(name = "shift_flag")
    private String shiftFlag;
}
