package et.safaricom.agent_onboarding.config.kafka;

import org.apache.kafka.clients.admin.AdminClient;
import org.apache.kafka.clients.admin.AdminClientConfig;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.ApplicationRunner;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.annotation.EnableKafka;

import java.util.Collections;
import java.util.Map;
import java.util.concurrent.ExecutionException;

@Configuration
@EnableKafka
public class KafkaProducerConfig {
    @Value("${spring.kafka.bootstrap-servers}")
    private String bootstrapServers;
    //Hack:===================================================================================
    //     =========================[ TESTING THE CONNECTION ]================================
    //     ===================================================================================

    @Bean
    public ApplicationRunner kafkaHealthCheck() {
        return args -> {
            Map<String, Object> config = Collections.singletonMap(
                    AdminClientConfig.BOOTSTRAP_SERVERS_CONFIG, bootstrapServers);

            try (AdminClient adminClient = AdminClient.create(config)) {
                adminClient.listTopics().names().get();
                System.out.println("✅ Successfully connected to Kafka: " + bootstrapServers);
            } catch (InterruptedException | ExecutionException e) {
                System.err.println("❌ Failed to connect to Kafka: " + bootstrapServers);
                e.printStackTrace();
            }
        };
    }
}