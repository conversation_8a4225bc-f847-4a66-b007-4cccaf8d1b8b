package et.safaricom.agent_onboarding.model.view.snd;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Lob;
import lombok.Getter;
import lombok.ToString;
import org.hibernate.annotations.Immutable;
import org.hibernate.annotations.Subselect;

import java.time.LocalDateTime;

@Getter
@ToString
@Entity
@Immutable
@JsonIgnoreProperties(ignoreUnknown = true)
@Subselect("""
    SELECT user_id, user_type_id, user_zone_id, user_hub_id, user_area_id, user_contact_no, user_email_id, user_address1, user_address2, user_address3, user_postal_code, user_parent_id, user_status, user_longitude, user_latitude,
           "comments", suspend_id, user_secondary_contact, add_by, staff, user_agency, postal_code, pin_no, vat_no, national_id, start_date, end_date, dept, activator_name, post, owner_name, activation_code, user_category,
           dob, user_loc_id, suspend_remarks, danger_zone, first_name, last_name, biometric_id, send_to_bi, file_name, active_user_name, remarks, status_changed_by, area_sales_manager, remedy_ref, id_number, ekyc_flag,
           commission_number, mpesa_flag, suspend_date, termination_date, nid_number, nid_waiting_card_number
    FROM onboardings.snd_user_detail
""")
public class SalesUserDetail {
    @Id
    @Column(name = "user_id")
    private String userId;

    @Column(name = "user_type_id")
    private Double userTypeId;

    @Column(name = "user_zone_id")
    private Double userZoneId;

    @Column(name = "user_hub_id")
    private Double userHubId;

    @Column(name = "user_area_id")
    private Double userAreaId;

    @Column(name = "user_contact_no")
    private String userContactNo;

    @Column(name = "user_email_id")
    private String userEmailId;

    @Column(name = "user_address1")
    private String userAddress1;

    @Column(name = "user_address2")
    private String userAddress2;

    @Column(name = "user_address3")
    private String userAddress3;

    @Column(name = "user_postal_code")
    private String userPostalCode;

    @Column(name = "user_parent_id")
    private String userParentId;

    @Column(name = "user_status")
    private Double userStatus;

    @Column(name = "user_longitude")
    private String userLongitude;

    @Column(name = "user_latitude")
    private String userLatitude;

    @Column(name = "comments", columnDefinition = "text")
    private String comments;

    @Column(name = "suspend_id")
    private Double suspendId;

    @Column(name = "user_secondary_contact")
    private String userSecondaryContact;

    @Column(name = "add_by")
    private String addBy;

    @Column(name = "staff")
    private Integer staff;

    @Column(name = "user_agency")
    private String userAgency;

    @Column(name = "postal_code")
    private String postalCode;

    @Column(name = "pin_no")
    private String pinNo;

    @Column(name = "vat_no")
    private String vatNo;

    @Column(name = "national_id")
    private String nationalId;

    @Column(name = "start_date")
    private LocalDateTime startDate;

    @Column(name = "end_date")
    private LocalDateTime endDate;

    @Column(name = "dept")
    private String dept;

    @Column(name = "activator_name")
    private String activatorName;

    @Column(name = "post")
    private String post;

    @Column(name = "owner_name")
    private String ownerName;

    @Column(name = "activation_code")
    private String activationCode;

    @Column(name = "user_category")
    private Integer userCategory;

    @Column(name = "dob")
    private LocalDateTime dob;

    @Column(name = "user_loc_id")
    private Double userLocId;

    @Column(name = "suspend_remarks")
    private String suspendRemarks;

    @Column(name = "danger_zone")
    private String dangerZone;

    @Column(name = "first_name")
    private String firstName;

    @Column(name = "last_name")
    private String lastName;

    @Column(name = "biometric_id")
    private String biometricId;

    @Column(name = "send_to_bi")
    private String sendToBi;

    @Column(name = "file_name")
    private String fileName;

    @Column(name = "active_user_name")
    private String activeUserName;

    @Column(name = "remarks")
    private String remarks;

    @Column(name = "status_changed_by")
    private String statusChangedBy;

    @Column(name = "area_sales_manager")
    private String areaSalesManager;

    @Column(name = "remedy_ref")
    private String remedyRef;

    @Column(name = "id_number")
    private String idNumber;

    @Column(name = "ekyc_flag")
    private String ekycFlag;

    @Column(name = "commission_number")
    private String commissionNumber;

    @Column(name = "mpesa_flag")
    private String mpesaFlag;

    @Column(name = "suspend_date")
    private LocalDateTime suspendDate;

    @Column(name = "termination_date")
    private LocalDateTime terminationDate;

    @Column(name = "nid_number")
    private String nidNumber;

    @Column(name = "nid_waiting_card_number")
    private String nidWaitingCardNumber;
}

//
//package et.safaricom.agent_onboarding.model.view.snd;
//
//import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
//import jakarta.persistence.Column;
//import jakarta.persistence.Entity;
//import jakarta.persistence.Id;
//import jakarta.persistence.Lob;
//import lombok.Getter;
//import lombok.ToString;
//import org.hibernate.annotations.Immutable;
//import org.hibernate.annotations.Subselect;
//
//import java.time.LocalDateTime;
//
//@Getter
//@ToString
//@Entity
//@Immutable
//@JsonIgnoreProperties(ignoreUnknown = true)
//@Subselect("""
//    SELECT user_id, user_type_id,  division, region, cluster, created_by , distributor ,  distributor_id  ,user_zone_id, user_hub_id, user_area_id, user_contact_no, user_email_id, user_address1, user_address2, user_address3, user_postal_code, user_parent_id, user_status, user_longitude, user_latitude,
//           "comments", suspend_id, user_secondary_contact, add_by, staff, user_agency, postal_code, pin_no, vat_no, national_id, start_date, end_date, dept, activator_name, post, owner_name, activation_code, user_category,
//           dob, user_loc_id, suspend_remarks, danger_zone, first_name, last_name, biometric_id, send_to_bi, file_name, active_user_name, remarks, status_changed_by, area_sales_manager, remedy_ref, id_number, ekyc_flag,
//           commission_number, mpesa_flag, suspend_date, termination_date, nid_number, nid_waiting_card_number
//    FROM onboardings.snd_user_detail
//""")
//public class SalesUserDetail {
//    @Id
//    @Column(name = "user_id")
//    private String userId;
//
//    @Column(name = "user_type_id")
//    private Double userTypeId;
//
//    @Column(name = "division")
//    private Double division;
//
//    @Column(name = "region")
//    private Double region;
//
//    @Column(name = "cluster")
//    private Double cluster;
//
//    @Column(name = "created_by")
//    private Double created_By;
//
//    @Column(name = "distributor")
//    private String distributor;
//
//    @Column(name = "distributor_id")
//    private String distributorId;
//
//
//    @Column(name = "user_zone_id")
//    private Double userZoneId;
//
//    @Column(name = "user_hub_id")
//    private Double userHubId;
//
//    @Column(name = "user_area_id")
//    private Double userAreaId;
//
//    @Column(name = "user_contact_no")
//    private String userContactNo;
//
//    @Column(name = "user_email_id")
//    private String userEmailId;
//
//    @Column(name = "user_address1")
//    private String userAddress1;
//
//    @Column(name = "user_address2")
//    private String userAddress2;
//
//    @Column(name = "user_address3")
//    private String userAddress3;
//
//    @Column(name = "user_postal_code")
//    private String userPostalCode;
//
//    @Column(name = "user_parent_id")
//    private String userParentId;
//
//    @Column(name = "user_status")
//    private Double userStatus;
//
//    @Column(name = "user_longitude")
//    private String userLongitude;
//
//    @Column(name = "user_latitude")
//    private String userLatitude;
//
//    @Column(name = "comments", columnDefinition = "text")
//    private String comments;
//
//    @Column(name = "suspend_id")
//    private Double suspendId;
//
//    @Column(name = "user_secondary_contact")
//    private String userSecondaryContact;
//
//    @Column(name = "add_by")
//    private String addBy;
//
//    @Column(name = "staff")
//    private Integer staff;
//
//    @Column(name = "user_agency")
//    private String userAgency;
//
//    @Column(name = "postal_code")
//    private String postalCode;
//
//    @Column(name = "pin_no")
//    private String pinNo;
//
//    @Column(name = "vat_no")
//    private String vatNo;
//
//    @Column(name = "national_id")
//    private String nationalId;
//
//    @Column(name = "start_date")
//    private LocalDateTime startDate;
//
//    @Column(name = "end_date")
//    private LocalDateTime endDate;
//
//    @Column(name = "dept")
//    private String dept;
//
//    @Column(name = "activator_name")
//    private String activatorName;
//
//    @Column(name = "post")
//    private String post;
//
//    @Column(name = "owner_name")
//    private String ownerName;
//
//    @Column(name = "activation_code")
//    private String activationCode;
//
//    @Column(name = "user_category")
//    private Integer userCategory;
//
//    @Column(name = "dob")
//    private LocalDateTime dob;
//
//    @Column(name = "user_loc_id")
//    private Double userLocId;
//
//    @Column(name = "suspend_remarks")
//    private String suspendRemarks;
//
//    @Column(name = "danger_zone")
//    private String dangerZone;
//
//    @Column(name = "first_name")
//    private String firstName;
//
//    @Column(name = "last_name")
//    private String lastName;
//
//    @Column(name = "biometric_id")
//    private String biometricId;
//
//    @Column(name = "send_to_bi")
//    private String sendToBi;
//
//    @Column(name = "file_name")
//    private String fileName;
//
//    @Column(name = "active_user_name")
//    private String activeUserName;
//
//    @Column(name = "remarks")
//    private String remarks;
//
//    @Column(name = "status_changed_by")
//    private String statusChangedBy;
//
//    @Column(name = "area_sales_manager")
//    private String areaSalesManager;
//
//    @Column(name = "remedy_ref")
//    private String remedyRef;
//
//    @Column(name = "id_number")
//    private String idNumber;
//
//    @Column(name = "ekyc_flag")
//    private String ekycFlag;
//
//    @Column(name = "commission_number")
//    private String commissionNumber;
//
//    @Column(name = "mpesa_flag")
//    private String mpesaFlag;
//
//    @Column(name = "suspend_date")
//    private LocalDateTime suspendDate;
//
//    @Column(name = "termination_date")
//    private LocalDateTime terminationDate;
//
//    @Column(name = "nid_number")
//    private String nidNumber;
//
//    @Column(name = "nid_waiting_card_number")
//    private String nidWaitingCardNumber;
//}

