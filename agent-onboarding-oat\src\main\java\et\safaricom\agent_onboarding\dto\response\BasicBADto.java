package et.safaricom.agent_onboarding.dto.response;

import et.safaricom.agent_onboarding.model.BAOnboardingDetails;
import java.time.LocalDateTime;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * Simplified DTO for Business Agent information
 */
public class BasicBADto {
    private final Long id;  // Add this field
    private final String agentId;
    private final String fullName;
    private final String msisdn;
    private final String status;
    private final LocalDateTime createdOn;
    private final boolean isReverified;
    private final String distributorName;
    private final String region;

    public BasicBADto(Long id, BAOnboardingDetails ba) {
        this.id = id;  // Initialize the id field
        this.agentId = ba.getAgentId();
        this.fullName = formatName(ba.getCrmFirstName(),
                ba.getCrmMiddleName(), ba.getCrmLastName());
        this.msisdn = ba.getMsisdn();
        this.status = ba.getStatus().name();
        this.createdOn = ba.getCreatedOn();
        this.isReverified = Boolean.TRUE.equals(ba.getIsReverified());
        this.region = ba.getRegion();
        this.distributorName = resolveDistributorName(ba.getDistributorId());
    }


    public Long getId() {
        return id;
    }

    private String formatName(String first, String middle, String last) {
        return Stream.of(first, middle, last)
                .filter(Objects::nonNull)
                .filter(s -> !s.isEmpty())
                .collect(Collectors.joining(" "));
    }


    private String resolveDistributorName(Long distributorId) {

        return distributorId != null ? "Distributor-" + distributorId
                : "Unknown Distributor";
    }


    public String getAgentId() {
        return agentId;
    }

    public String getFullName() {
        return fullName;
    }

    public String getMsisdn() {
        return msisdn;
    }

    public String getStatus() {
        return status;
    }

    public LocalDateTime getCreatedOn() {
        return createdOn;
    }

    public boolean isReverified() {
        return isReverified;
    }

    public String getDistributorName() {
        return distributorName;
    }

    public String getRegion() {
        return region;
    }

    // ========== toString ========== //

    @Override
    public String toString() {
        return "BasicBADto{" +
                "id=" + id +
                ", agentId='" + agentId + '\'' +
                ", fullName='" + fullName + '\'' +
                ", msisdn='" + msisdn + '\'' +
                ", status='" + status + '\'' +
                ", createdOn=" + createdOn +
                ", isReverified=" + isReverified +
                ", distributorName='" + distributorName + '\'' +
                ", region='" + region + '\'' +
                '}';
    }
}

