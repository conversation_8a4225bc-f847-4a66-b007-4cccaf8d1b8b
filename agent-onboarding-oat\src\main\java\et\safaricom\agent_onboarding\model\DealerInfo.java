package et.safaricom.agent_onboarding.model;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Entity
@Table(name = "dealer_info", schema = "onboardings")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DealerInfo {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "dealer_name")
    private String dealerName;

    @Column(name = "old_short_code")
    private String oldShortCode;

    @Column(name = "dealer_code")
    private String dealerCode;

    @Column(name = "new_short_code")
    private String newShortCode;
}
