package et.safaricom.agent_onboarding.utils;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;

@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
@Builder
@ToString
public class ResponseTemplate<T> {
    @JsonProperty("success")
    private boolean success;

    @JsonProperty("message")
    private String message;

    @JsonProperty("status")
    private Integer status;

    @JsonProperty("data")
    private T data;

    public static <T> ResponseTemplate<T> empty() {
        return success(null);
    }

    public static <T> ResponseTemplate<T> success(T data) {
        return ResponseTemplate.<T>builder()
                .message("Success!")
                .status(0)
                .data(data)
                .success(true)
                .build();
    }

    public static <T> ResponseTemplate<T> error(String message) {
        return ResponseTemplate.<T>builder()
                .message(message)
                .status(1)
                .success(false)
                .build();
    }
}
