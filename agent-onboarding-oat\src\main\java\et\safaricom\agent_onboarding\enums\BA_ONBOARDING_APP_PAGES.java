package et.safaricom.agent_onboarding.enums;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;

public enum BA_ONBOARDING_APP_PAGES {
    GENERAL_INFORMATION_PAGE(Arrays.asList("crmBiometricsId", "crmFirstName", "crmMiddleName", "crmLastName", "msisdn")),
    PERSONAL_INFORMATION_PAGE(Arrays.asList("faydaNumber", "nidSubjectId", "nidFullName", "nidDateOfBirth", "nidIDExpirationDate", "nidAddress", "nidPhotoPath", "letterOfConsentList")),
    DISTRIBUTION_INFORMATION_PAGE(Arrays.asList("rsmUsername", "ramUsername", "distributorId", "distributorShopId", "latitude", "longitude", "division", "region", "cluster", "route", "site")),
    CAPTURE_PHOTO_PAGE(List.of("agentPhotoList")),
    BIOMETRICS_PAGE(Arrays.asList("biometricsIdList", "biometricsList")),
    NID_PICTURE_PAGE(List.of("nidCardPictureList"));

    private final List<String> fields;

    BA_ONBOARDING_APP_PAGES(List<String> fields) {
        this.fields = fields;
    }

    public List<String> getFields() {
        return fields;
    }

    public static Optional<BA_ONBOARDING_APP_PAGES> getPageByField(String fieldName) {
        return Arrays.stream(values())
                .filter(page -> page.fields.contains(fieldName))
                .findFirst();
    }

    public static List<String> getFieldsByPage(BA_ONBOARDING_APP_PAGES page) {
        return page.getFields();
    }
}
