package et.safaricom.agent_onboarding.repository.impl;

import et.safaricom.agent_onboarding.model.BAOnboardingDetails;
import et.safaricom.agent_onboarding.repository.CustomBAOnboardingDetailsRepository;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public class CustomBAOnboardingDetailsRepositoryImpl implements CustomBAOnboardingDetailsRepository {

    @PersistenceContext
    private EntityManager entityManager;

    @Override
    public Optional<BAOnboardingDetails> findOneByPriority(String msisdn, String biometricsId) {
        String jpql = "SELECT b FROM BAOnboardingDetails b WHERE b.msisdn = :msisdn";
        List<BAOnboardingDetails> results = entityManager.createQuery(jpql, BAOnboardingDetails.class)
                .setParameter("msisdn", msisdn)
                .setMaxResults(1)
                .getResultList();

        if (!results.isEmpty()) return Optional.of(results.get(0));

        jpql = "SELECT b FROM BAOnboardingDetails b WHERE b.crmBiometricsId = :crmBiometricsId";
        results = entityManager.createQuery(jpql, BAOnboardingDetails.class)
                .setParameter("crmBiometricsId", biometricsId)
                .setMaxResults(1)
                .getResultList();

        return results.isEmpty() ? Optional.empty() : Optional.of(results.get(0));
    }

    @Override
    public Optional<BAOnboardingDetails> findOneByPriority(String agentId, String msisdn, String biometricsId) {
        String jpql = "SELECT b FROM BAOnboardingDetails b WHERE b.agentId = :agentId";
        List<BAOnboardingDetails> results = entityManager.createQuery(jpql, BAOnboardingDetails.class)
                .setParameter("agentId", agentId)
                .setMaxResults(1)
                .getResultList();

        if (!results.isEmpty()) return Optional.of(results.get(0));

        jpql = "SELECT b FROM BAOnboardingDetails b WHERE b.msisdn = :msisdn";
        results = entityManager.createQuery(jpql, BAOnboardingDetails.class)
                .setParameter("msisdn", msisdn)
                .setMaxResults(1)
                .getResultList();

        if (!results.isEmpty()) return Optional.of(results.get(0));

        jpql = "SELECT b FROM BAOnboardingDetails b WHERE b.crmBiometricsId = :crmBiometricsId";
        results = entityManager.createQuery(jpql, BAOnboardingDetails.class)
                .setParameter("crmBiometricsId", biometricsId)
                .setMaxResults(1)
                .getResultList();

        return results.isEmpty() ? Optional.empty() : Optional.of(results.get(0));
    }
}

