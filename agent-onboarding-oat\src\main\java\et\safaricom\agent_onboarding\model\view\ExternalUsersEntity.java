package et.safaricom.agent_onboarding.model.view;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import et.safaricom.agent_onboarding.enums.UM_USER_STATUS;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.ToString;
import org.hibernate.annotations.Immutable;
import org.hibernate.annotations.Subselect;

import java.util.List;

@Getter
@ToString
@Entity
@Immutable
@JsonIgnoreProperties(ignoreUnknown = true)
@Subselect("""
    SELECT exu.id, exu.short_code, exu.first_name, exu.middle_name, exu.last_name, exu.user_name, exu.parent_id, exu.user_hierarchy, exu.alternate_locations,
    exu.channel_id, exu.location_id, exu.contact_phone, tl.location_name, tlt.id as location_type_id, tlt.location_type_name, exu.is_active, exu.status
    FROM usersandcluster.tbl_external_user AS exu
    left join usersandcluster.tbl_locations tl on tl.id = exu.location_id
    left join usersandcluster.tbl_location_type tlt on tlt.id = tl.location_type
""")
public class ExternalUsersEntity {
    @Id
    @Column(name = "id")
    private Long id;

    @Column(name = "short_code")
    private String shortCode;

    @Column(name = "first_name")
    private String firstName;

    @Column(name = "middle_name")
    private String middleName;

    @Column(name = "last_name")
    private String lastName;

    @Column(name = "user_name")
    private String userName;

    @Column(name = "parent_id")
    private Long parentId;

    @Column(name = "user_hierarchy")
    private List<Long> userHierarchies;

    @Column(name = "alternate_locations")
    private List<Long> alternateLocations;

    @Column(name = "channel_id")
    private Long channelId;

    @Column(name = "location_id")
    private Long locationId;

    @Column(name = "contact_phone")
    private String contactPhone;

    @Column(name = "location_name")
    private String locationName;

    @Column(name = "location_type_id")
    private Long locationTypeId;

    @Column(name = "location_type_name")
    private String locationTypeName;

    @Column(name = "is_active")
    private Boolean isActive;

    @Column(name = "status")
    @Enumerated(EnumType.ORDINAL)
    private UM_USER_STATUS status;

}
