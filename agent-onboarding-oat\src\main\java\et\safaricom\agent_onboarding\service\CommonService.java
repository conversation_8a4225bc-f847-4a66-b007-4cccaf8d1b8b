package et.safaricom.agent_onboarding.service;

import com.mpesautils.logmanager.Log;
import com.mpesautils.logmanager.LogManager;
import et.safaricom.agent_onboarding.dto.response.UserDetailsResponseDto;
import et.safaricom.agent_onboarding.enums.UM_USER_STATUS;
import et.safaricom.agent_onboarding.model.view.ExternalUsersEntity;
import et.safaricom.agent_onboarding.model.view.InternalUsersEntity;
import et.safaricom.agent_onboarding.repository.view.ExternalUsersEntityRepository;
import et.safaricom.agent_onboarding.repository.view.InternalUsersEntityRepository;
import et.safaricom.agent_onboarding.utils.LoggerCommon;
import et.safaricom.agent_onboarding.utils.ResponseTemplate;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Optional;

@Service
@RequiredArgsConstructor
public class CommonService {
    private final LoggerCommon loggerCommon;

    public ResponseTemplate<UserDetailsResponseDto> getUserBasicDetails(Long id, String username, InternalUsersEntityRepository internalUsersEntityRepository, ExternalUsersEntityRepository externalUsersEntityRepository, LogManager logManager, Log log) {
        UserDetailsResponseDto userDetailsResponseDto = new UserDetailsResponseDto();
        Optional<InternalUsersEntity> internalUsersEntityOptional = (id != null && id > 0L) ? internalUsersEntityRepository.findById(id) : internalUsersEntityRepository.findByUserName(username);
        if (internalUsersEntityOptional.isEmpty()) {
            Optional<ExternalUsersEntity> externalUsersEntityOptional =  (id != null && id > 0L) ? externalUsersEntityRepository.findById(id) : externalUsersEntityRepository.findByUserName(username);
            if (externalUsersEntityOptional.isPresent()) {
                if (!externalUsersEntityOptional.get().getIsActive() && !externalUsersEntityOptional.get().getStatus().equals(UM_USER_STATUS.ACTIVE)) {
                    logManager.info(loggerCommon.getLog(log, "info", 1, "Unauthorized!", "User by the specified username [ " + username + " ] is not active."));
                    return ResponseTemplate.error("User is not active!");
                }

                getUserDetails(userDetailsResponseDto, externalUsersEntityOptional.get().getId(), externalUsersEntityOptional.get().getFirstName(), externalUsersEntityOptional.get().getMiddleName(), externalUsersEntityOptional.get().getLastName(), externalUsersEntityOptional.get().getContactPhone(), externalUsersEntityOptional.get().getLocationId());
                userDetailsResponseDto.setUserHierarchies(externalUsersEntityOptional.get().getUserHierarchies());
                userDetailsResponseDto.setAlternateLocations(externalUsersEntityOptional.get().getAlternateLocations());
                userDetailsResponseDto.setChannelId(externalUsersEntityOptional.get().getChannelId());
            }
            else {
                logManager.info(loggerCommon.getLog(log, "info", 1, "Unauthorized!", "User by the specified username [ " + username + " ] is either not found or not active."));
                return ResponseTemplate.error("User not found or not active!");
            }
        }
        else {
            if (!internalUsersEntityOptional.get().getIsActive() && !internalUsersEntityOptional.get().getStatus().equals(UM_USER_STATUS.ACTIVE)) {
                logManager.info(loggerCommon.getLog(log, "info", 1, "Unauthorized!", "User by the specified username [ " + username + " ] is not active."));
                return ResponseTemplate.error("User is not active!");
            }
            getUserDetails(userDetailsResponseDto, internalUsersEntityOptional.get().getId(), internalUsersEntityOptional.get().getFirstName(), internalUsersEntityOptional.get().getMiddleName(), internalUsersEntityOptional.get().getLastName(), internalUsersEntityOptional.get().getContactPhone(), internalUsersEntityOptional.get().getLocationId());
            userDetailsResponseDto.setUserHierarchies(internalUsersEntityOptional.get().getUserHierarchy());
            userDetailsResponseDto.setAlternateLocations(internalUsersEntityOptional.get().getAlternateLocations());
        }
        return ResponseTemplate.success(userDetailsResponseDto);
    }

    private static void getUserDetails(UserDetailsResponseDto userDetailsResponseDto, Long id, String firstName, String middleName, String lastName, String contactPhone, Long locationId) {
        userDetailsResponseDto.setId(id);
        userDetailsResponseDto.setFirstName(firstName);
        userDetailsResponseDto.setMiddleName(middleName);
        userDetailsResponseDto.setLastName(lastName);
        userDetailsResponseDto.setContactPhone(contactPhone);
        userDetailsResponseDto.setLocationId(locationId);
    }
}
