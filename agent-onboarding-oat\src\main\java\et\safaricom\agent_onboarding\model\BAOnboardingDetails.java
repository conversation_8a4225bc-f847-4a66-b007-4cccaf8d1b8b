package et.safaricom.agent_onboarding.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import et.safaricom.agent_onboarding.enums.APPLICATION_STATUS;
import et.safaricom.agent_onboarding.enums.USER_STATUS;
import jakarta.persistence.*;
import lombok.*;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

@Entity
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "tbl_ba_onboarding_details", schema = "onboardings")
public class BAOnboardingDetails {
    @Id
    @Column(name = "id", unique = true, updatable = false)
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "session_id", unique = true, updatable = false)
    private UUID sessionId;

    @Column(name = "agent_id")
    private String agentId; // First Name (First Character) + Last Name + (Integer If not unique)

//    Note: GENERAL_INFORMATION_PAGE
    @Column(name = "crm_biometrics_id", unique = true)
    private String crmBiometricsId;

    @Column(name = "crm_first_name")
    private String crmFirstName;

    @Column(name = "crm_middle_name")
    private String crmMiddleName;

    @Column(name = "crm_last_name")
    private String crmLastName;

    @Column(name = "msisdn")
    private String msisdn; // Validate to Store Only 251

//  Note: PERSONAL_INFORMATION_PAGE
    @Column(name = "fayda_number")
    private String faydaNumber;

    @Column(name = "nid_subject_id") // , unique = true
    private String nidSubjectId;

    @Column(name = "nid_first_name")
    private String nidFirstName;

    @Column(name = "nid_middle_name")
    private String nidMiddleName;

    @Column(name = "nid_last_name")
    private String nidLastName;

    @Column(name = "nid_date_of_birth")
    private LocalDate nidDateOfBirth;

    @Column(name = "nid_id_expiration_date")
    private LocalDate nidIDExpirationDate;

    @Embedded
    @Column(name = "nid_address")
    private Address nidAddress;

    @Column(name = "nid_photo_path")
    private String nidPhotoPath;

    @ElementCollection
    @Column(name = "letter_of_consent_list")
    @CollectionTable(name = "tbl_letter_of_consent_id", schema = "onboardings", joinColumns = @JoinColumn(name = "ba_onboarding_id"))
    private List<String> letterOfConsentList = new ArrayList<>();

//  Note: DISTRIBUTION_INFORMATION_PAGE
    @Column(name = "rsm")
    private String rsm;

    @Column(name = "ram")
    private String ram;

    @Column(name = "roles")
    private String roles;

    @Column(name = "distributor_id")
    private Long distributorId;

    @Column(name = "distributor_shop_id")
    private Long distributorShopId;

    @Column(name = "latitude")
    private Double latitude;

    @Column(name = "longitude")
    private Double longitude;

    @Column(name = "division")
    private String division;

    @Column(name = "region")
    private String region;

    @Column(name = "cluster")
    private String cluster;

    @Column(name = "route")
    private String route;

    @Column(name = "site")
    private String site;

//    Note: CAPTURE_PHOTO_PAGE
    @ElementCollection
    @Column(name = "agent_photo_list")
    @CollectionTable(name = "tbl_agent_photo_id", schema = "onboardings", joinColumns = @JoinColumn(name = "ba_onboarding_id"))
    private List<String> agentPhotoList = new ArrayList<>();

//    Note: BIOMETRICS_PAGE
    @ElementCollection
    @Column(name = "biometric_id_list")
    @CollectionTable(name = "tbl_biometrics_id", schema = "onboardings", joinColumns = @JoinColumn(name = "ba_onboarding_id"))
    private List<String> biometricsIdList = new ArrayList<>();

    @ElementCollection
    @Column(name = "biometrics_list")
    @CollectionTable(name = "tbl_biometrics", schema = "onboardings", joinColumns = @JoinColumn(name = "ba_onboarding_id"))
    private List<BiometricsData> biometricsList = new ArrayList<>();

//    Note: NID_PICTURE_PAGE
    @ElementCollection
    @Column(name = "nid_card_picture_list")
    @CollectionTable(name = "tbl_nid_card_pictures", schema = "onboardings", joinColumns = @JoinColumn(name = "ba_onboarding_id"))
    private List<NIDCardPicture> nidCardPictureList = new ArrayList<>();

//    Note: No Page
    @Column(name = "approved_by")
    private String approvedBy;

    @Column(name = "approved_by_id")
    private Long approvedById;

    @Enumerated(EnumType.STRING)
    @Column(name = "status")
    private APPLICATION_STATUS status;

    @Enumerated(EnumType.STRING)
    @Column(name = "user_status")
    private USER_STATUS userStatus;

    @Column(name = "approved_on", nullable = true)
    private LocalDateTime approvedOn;

    @Column(name = "updated_by")
    private String updatedBy;

    @Column(name = "updated_by_id")
    private Long updatedById;

    @UpdateTimestamp
    @Column(name = "updated_on")
    private LocalDateTime updatedOn;

    @Column(name = "created_by", updatable = false, nullable = false)
    private String createdBy;

    @Column(name = "snd_created_by")
    private String sndCreatedBy;

    @Column(name = "snd_distributor")
    private String sndDistributor;

    @Column(name = "snd_distributor_id")
    private String sndDistributorId;

    @Column(name = "created_by_id")
    private Long createdById;

    @CreationTimestamp
    @Column(name = "created_on", updatable = false, nullable = false)
    private LocalDateTime createdOn;

    @ElementCollection
    @Column(name = "bo_remark_list")
    @CollectionTable(name = "tbl_bo_remark_id", schema = "onboardings", joinColumns = @JoinColumn(name = "ba_onboarding_id"))
    private List<String> boRemarkList = new ArrayList<>();

    @ElementCollection
    @Column(name = "bo_updatable_field_list")
    @CollectionTable(name = "tbl_bo_updatable_fields", schema = "onboardings", joinColumns = @JoinColumn(name = "ba_onboarding_id"))
    private List<BOUpdatableFields> boUpdatableFieldList = new ArrayList<>();

    @Column(name = "is_agent_created_on_ekyc")
    private Boolean isAgentCreatedOnEkyc;

    @Column(name = "isA_agent_created_on_dxl")
    private Boolean isAgentCreatedOnDxl;

    @Column(name = "is_agent_created_on_bd")
    private Boolean isAgentCreatedOnBD; // Wondering what BD is; It's Big Data

    @Column(name = "is_reverified")
    private Boolean isReverified;

    @Column(name = "reverified_on")
    private LocalDateTime reverifiedOn;


    @Column(name ="reason_for_user_update")
    private String reasonForUserUpdate;

    @Column (name ="reason_for_application_status_update")
    private String reasonForApplicationStatusUpdate;

    @PrePersist
    protected void generateSessionId() {
        if (sessionId == null) {
            sessionId = UUID.randomUUID(); // Assign a random UUID before persisting
        }

        isAgentCreatedOnEkyc = false;
        isAgentCreatedOnDxl = false;
        isAgentCreatedOnBD = false;

        if (isReverified == null) {
            isReverified = false;
        }
    }
}
