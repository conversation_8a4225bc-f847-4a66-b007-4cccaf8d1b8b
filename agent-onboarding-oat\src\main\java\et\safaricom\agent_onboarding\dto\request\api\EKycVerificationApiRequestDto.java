package et.safaricom.agent_onboarding.dto.request.api;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.List;

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class EKycVerificationApiRequestDto {
    @JsonProperty("biometricData")
    private List<BiometricData> biometricData;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class BiometricData {
        @JsonProperty("biometricType")
        private String biometricType;

        @JsonProperty("biometricSubType")
        private String biometricSubType;

        @JsonProperty("instance")
        private String instance;

        @JsonProperty("image")
        private String image;

        @JsonProperty("captureDate")
        private String captureDate;

        @JsonProperty("captureDevice")
        private String captureDevice;

        @JsonProperty("impressionType")
        private String impressionType;

        @JsonProperty("compression")
        private String compression;

        @JsonProperty("metadata")
        private String metadata;

        @JsonProperty("comment")
        private String comment;
    }
}
