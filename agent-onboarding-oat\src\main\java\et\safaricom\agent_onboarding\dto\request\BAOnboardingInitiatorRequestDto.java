package et.safaricom.agent_onboarding.dto.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

@Data
@ToString
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class BAOnboardingInitiatorRequestDto {
    @JsonProperty(value = "phoneNumber", required = true)
    private String phoneNumber;
}
