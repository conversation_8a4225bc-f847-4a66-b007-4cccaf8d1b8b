package et.safaricom.agent_onboarding.controller;

import com.mpesautils.logmanager.LogManager;
import et.safaricom.agent_onboarding.dto.request.AgentInfoRequestDto;
import et.safaricom.agent_onboarding.dto.request.BAOnboardingInitiatorRequestDto;
import et.safaricom.agent_onboarding.dto.request.BAOnboardingUpdateRequestDto;
import et.safaricom.agent_onboarding.dto.request.BAReverificationInitiatorRequestDto;
import et.safaricom.agent_onboarding.dto.request.filter.ListUsersFilterParamsRequestDto;
import et.safaricom.agent_onboarding.dto.response.BasicBADto;
import et.safaricom.agent_onboarding.enums.INITIATION_TYPE;
import et.safaricom.agent_onboarding.enums.OPTIONAL_PARAM;
import et.safaricom.agent_onboarding.enums.STATISTICS_TYPE;
import et.safaricom.agent_onboarding.service.BAOnboardingService;
//import et.safaricom.agent_onboarding.service.BAReverifiedOnboardingService;
import et.safaricom.agent_onboarding.service.SecondaryVerificationService;
import et.safaricom.agent_onboarding.utils.ResponseTemplate;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;


@RestController
@RequiredArgsConstructor
@RequestMapping("/api/v1/ba-onboarding")
@SecurityRequirement(name = "One Platform Token")
public class BAOnboardingController {
    private final BAOnboardingService baOnboardingService;
    private final SecondaryVerificationService secondaryVerificationService;

    @PostMapping("/create")
    @Operation(summary = "Initiate BA Onboarding", tags = "BA Onboarding", description = "An API for initiating the BA Onboarding Process")
    public ResponseTemplate<?> initiateBAOnboarding(@RequestBody BAOnboardingInitiatorRequestDto requestDto) {
        LogManager logManager = new LogManager();
        logManager.startClock();

        return baOnboardingService.initiateBAOnboardingOrReverification(requestDto, null, INITIATION_TYPE.ONBOARDING, logManager);
    }

    @PostMapping("/re-verify")
    @Operation(summary = "Initiate BA Reverification", tags = "BA Onboarding", description = "An API for initiating the BA Reverification Process")
    public ResponseTemplate<?> initiateBAReverification(@RequestBody BAReverificationInitiatorRequestDto requestDto) {
        LogManager logManager = new LogManager();
        logManager.startClock();

        return baOnboardingService.initiateBAOnboardingOrReverification(null, requestDto, INITIATION_TYPE.RE_VERIFICATION, logManager);
    }

//    @GetMapping("/snd-info")
//    @Operation(summary = "Get SnD BA Information", tags = "BA Onboarding", description = "An API for fetching BA Information from SnD dump for Reverification Process.")
//    public ResponseTemplate<?> getSnDBAInformation(@RequestParam("agent-id") String agentId) {
//        LogManager logManager = new LogManager();
//        logManager.startClock();
//
//        return baOnboardingService.getSnDBAInformation(agentId, logManager);
//    }


    @GetMapping("/snd-info")
    @Operation(summary = "Get SnD BA Information", tags = "BA Onboarding", description = "An API for fetching BA Information from SnD dump for Reverification Process.")
    public ResponseTemplate<?> getnewSnDBAInformation(@RequestParam("agent-id") String agentId) {
        LogManager logManager = new LogManager();
        logManager.startClock();
        return secondaryVerificationService.getNewSnDBAInformation(agentId, logManager);
    }

    @PutMapping("/update")
    @Operation(summary = "Update BA Onboarding Details", tags = "BA Onboarding", description = "An API for updating BA Onboarding Details.")
    @ApiResponses({
            @ApiResponse(responseCode = "200", description = "Success",
                    content = @Content(mediaType = "application/json",
                            schema = @Schema(example = "{\"success\": true, \"status\": 0, \"message\": \"Success!\", \"data\": {\"id\": 1, \"sessionId\": \"minne-maynee-myneee-moooo\", \"pageId\": \"BIOMETRICS_PAGE\", \"data\": {}}}"))),
            @ApiResponse(responseCode = "200", description = "Failure",
                    content = @Content(mediaType = "application/json",
                            schema = @Schema(example = "{\"status\": 1, \"message\": \"Something went wrong! please try again.\", \"data\": null}")))
    })
    public ResponseTemplate<?> continueBAOnboarding(
            @Parameter(
                    name = "User-Agent",
                    description = "Identifies the source of the request (Mobile App or Web Browser)",
                    example = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
                    in = ParameterIn.HEADER,
                    schema = @Schema(type = "string")
            )
            @RequestHeader("User-Agent") String userAgent,
            @RequestParam(value = "options", required = false) OPTIONAL_PARAM options,
            @RequestBody BAOnboardingUpdateRequestDto requestDto
    ) {
        LogManager logManager = new LogManager();
        logManager.startClock();

        return baOnboardingService.baOnboardingUpdateRouter(requestDto, userAgent, options, logManager);
    }


    @GetMapping("/list")
    @Operation(summary = "List an Onboarded Agents", tags = "BA Onboarding", description = "An API for listing an onboarded agents on both [ Web & App ].")
    public ResponseTemplate<?> listUsers(
            @Parameter(
                    name = "User-Agent",
                    description = "Identifies the source of the request (Mobile App or Web Browser)",
                    example = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
                    in = ParameterIn.HEADER,
                    schema = @Schema(type = "string")
            )
            @RequestHeader("User-Agent") String userAgent,
            @ModelAttribute ListUsersFilterParamsRequestDto filteringCriteria

            ) {
        LogManager logManager = new LogManager();
        logManager.startClock();

        return baOnboardingService.listUsersRouter(userAgent, filteringCriteria, logManager);
    }

    @GetMapping("/detail")
    @Operation(summary = "Onboarded Agent Detail", tags = "BA Onboarding", description = "An API for fetching the details of an onboarded agents on both [ Web & App ].")
    public ResponseTemplate<?> userDetail(
            @Parameter(
                    name = "User-Agent",
                    description = "Identifies the source of the request (Mobile App or Web Browser)",
                    example = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
                    in = ParameterIn.HEADER,
                    schema = @Schema(type = "string")
            )
            @RequestHeader("User-Agent") String userAgent,
            @RequestParam("id") Long id
    ) {
        LogManager logManager = new LogManager();
        logManager.startClock();

        return baOnboardingService.userDetailRouter(userAgent, id, logManager);
    }

    @PostMapping("/agent-info")
    @Operation(summary = "Onboarded Agent Detail by agent-id", tags = "BA Onboarding", description = "An API for fetching the details of an onboarded agents by agent-id.")
    public Object userDetailByAgentId(
            @Parameter(
                    name = "User-Agent",
                    description = "Identifies the source of the request (Mobile App or Web Browser)",
                    example = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
                    in = ParameterIn.HEADER,
                    schema = @Schema(type = "string")
            )
            @RequestHeader(value = "User-Agent", required = false) String userAgent,
            @RequestBody AgentInfoRequestDto agentInfoRequestDto
    ) {
        LogManager logManager = new LogManager();
        logManager.startClock();

        return baOnboardingService.userDetailByAgentId(userAgent, agentInfoRequestDto, logManager);
    }

    @GetMapping("/required-fields")
    @Operation(summary = "Onboarded Agent Required Fields", tags = "BA Onboarding", description = "An API for fetching the required fields of an onboarded agents on the App.")
    public ResponseTemplate<?> userRequiredFields(@RequestParam("id") Long id) {
        LogManager logManager = new LogManager();
        logManager.startClock();

        return baOnboardingService.userRequiredFields(id, logManager);
    }

    @GetMapping("/statistics")
    @Operation(summary = "Onboarded Agent Dashboard Statistics", tags = "BA Onboarding", description = "An API for fetching the statistics of an onboarded agents for the web dashboard.")
    public ResponseTemplate<?> getStatistics(@RequestParam(value = "options", required = false) STATISTICS_TYPE type) {
        LogManager logManager = new LogManager();
        logManager.startClock();

        return baOnboardingService.getStatisticsRouter(type, logManager);
    }

    @GetMapping("/reverified")
    @Operation(summary = "Get Re-verified BAs", tags = "BA Onboarding")
    public ResponseTemplate<List<BasicBADto>> getReverifiedBAs() {
        LogManager logManager = new LogManager();
        logManager.startClock();
        return baOnboardingService.getReverifiedBAs();
    }

    @GetMapping("/new")
    @Operation(summary = "Get New BAs", tags = "BA Onboarding")
    public ResponseTemplate<List<BasicBADto>> getNewBAs() {
        LogManager logManager = new LogManager();
        logManager.startClock();
        return baOnboardingService.getNewBAs();
    }


    @GetMapping("/statistics/new-agents")
    @Operation(summary = "Get statistics for new agents", tags = "BA Onboarding")
    public ResponseTemplate<?> getNewAgentsStatistics() {
        LogManager logManager = new LogManager();
        logManager.startClock();
        return baOnboardingService.getNewAgentsStatistics(logManager);
    }

    @GetMapping("/statistics/reverified-agents")
    @Operation(summary = "Get statistics for reverified agents", tags = "BA Onboarding")
    public ResponseTemplate<?> getReverifiedAgentsStatistics() {
        LogManager logManager = new LogManager();
        logManager.startClock();
        return baOnboardingService.getReverifiedAgentsStatistics(logManager);
    }

    @GetMapping("/list/report")
    @Operation(summary = "List Onboarded Agents For Reporting", tags = "BA Onboarding", description = "An API for listing an onboarded agents on both [ Web & App ].")
    public ResponseTemplate<?> listUsersReport(
            @Parameter(
                    name = "User-Agent",
                    description = "Identifies the source of the request (Mobile App or Web Browser)",
                    example = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
                    in = ParameterIn.HEADER,
                    schema = @Schema(type = "string")
            )
           @ModelAttribute ListUsersFilterParamsRequestDto filteringCriteria

    ) {
        LogManager logManager = new LogManager();
        logManager.startClock();

        return baOnboardingService.listUsersReportWebHandler( filteringCriteria, logManager);
    }
}
