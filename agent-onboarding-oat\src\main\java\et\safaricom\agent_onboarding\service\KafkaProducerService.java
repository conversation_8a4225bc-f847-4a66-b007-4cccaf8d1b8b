package et.safaricom.agent_onboarding.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.mpesautils.logmanager.Log;
import com.mpesautils.logmanager.LogManager;
import et.safaricom.agent_onboarding.dto.request.kafka.CreateAgentOnBDKafkaRequestDto;
import et.safaricom.agent_onboarding.utils.LoggerCommon;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class KafkaProducerService {
    private final LoggerCommon loggerCommon;
    private final KafkaTemplate<String, Object> kafkaTemplate;

    @Value("${spring.kafka.template.default-topic}")
    private String TOPIC = "Agent-Registration-Details";

    public void sendMessage(CreateAgentOnBDKafkaRequestDto createAgentOnBDKafkaRequestDto, LogManager logManager, Log log) throws Exception {
        System.err.println("Topic: " + TOPIC);
        try {
            log.setTargetSystem("BIG-DATA");
            kafkaTemplate.send(TOPIC, new ObjectMapper().writeValueAsString(createAgentOnBDKafkaRequestDto));
        } catch (Exception e) {
            e.printStackTrace();
            logManager.error(loggerCommon.getLog(log, 1, "Something went wrong! Please try again.", e));
            throw new Exception("Something went wrong on kafka producer: " + e.getMessage());
        }
        System.err.println("- Done -");
    }
}
