package et.safaricom.agent_onboarding.dto.request.api;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

import java.util.List;

@Data
@ToString
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class CreateAgentApiRequestDto {
    @JsonProperty("agentId")
    private String agentId;

    @JsonProperty("ekycId")
    private String ekycId;

    @JsonProperty("roles")
    private List<String> roles;

    @JsonProperty("timestamp")
    private String timestamp;
}
