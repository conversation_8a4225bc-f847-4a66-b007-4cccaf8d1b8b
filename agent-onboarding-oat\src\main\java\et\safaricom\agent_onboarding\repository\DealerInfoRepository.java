package et.safaricom.agent_onboarding.repository;

import et.safaricom.agent_onboarding.model.DealerInfo;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.Optional;

public interface DealerInfoRepository extends JpaRepository<DealerInfo, Long> {
    @Query("SELECT d.dealerCode FROM DealerInfo d WHERE d.oldShortCode = :oldShortCode")
    Optional<String> getDealerCodeByOldShortCode(@Param("oldShortCode") String oldShortCode);}
