package et.safaricom.agent_onboarding.controller;

import com.mpesautils.logmanager.LogManager;
import et.safaricom.agent_onboarding.enums.DROPDOWN_USER_ROLE;
import et.safaricom.agent_onboarding.service.OtherService;
import et.safaricom.agent_onboarding.utils.ResponseTemplate;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
@RequestMapping("/api/v1/ba-onboarding/other")
@SecurityRequirement(name = "One Platform Token")
public class OtherController {
    private final OtherService otherService;

    @GetMapping("/drop-down/users")
    @Operation(summary = "List Users", tags = "Drop Down", description = "An API for fetching users by role (RSM/RAM).")
    public ResponseTemplate<?> getUsersByRole(@RequestParam("userRole") DROPDOWN_USER_ROLE userRole, @RequestParam(value = "isLocationSpecific", required = false) Boolean isLocationSpecific) {
        LogManager logManager = new LogManager();
        logManager.startClock();

        return otherService.getUsers(isLocationSpecific, userRole, logManager);
    }

    @GetMapping("/drop-down/distributors")
    @Operation(summary = "List Channels", tags = "Drop Down", description = "An API for fetching distributors and distributor shops.")
    public ResponseTemplate<?> getChannels(@RequestParam(value = "distributorId", required = false) Long distributorId, @RequestParam(value = "isLocationSpecific", required = false) Boolean isLocationSpecific) {
        LogManager logManager = new LogManager();
        logManager.startClock();

        return otherService.getChannels(isLocationSpecific, distributorId, logManager);
    }

    @GetMapping("/drop-down/geo-spatial/location")
    @Operation(summary = "List Geo-Spatial Locations", tags = "Drop Down", description = "An API for fetching geo-spatial locations based on their hierarchy.")
    public ResponseTemplate<?> getGeoLocations(
            @RequestParam(value = "division", required = false) String division,
            @RequestParam(value = "region", required = false) String region,
            @RequestParam(value = "cluster", required = false) String cluster
    ) {
        LogManager logManager = new LogManager();
        logManager.startClock();

        return otherService.getGeoLocations(division, region, cluster, logManager);
    }

    @GetMapping("/drop-down/required-field/remarks")
    @Operation(summary = "List Pre-Defined Remarks", tags = "Drop Down", description = "An API for fetching pre-defined remarks for required-fields.")
    public ResponseTemplate<?> getBuiltInRemarks() {
        LogManager logManager = new LogManager();
        logManager.startClock();

        return otherService.getBuiltInRemarks(logManager);
    }

    @GetMapping("/test/kafka/send-message")
    @Operation(summary = "Produce Kafka Message", tags = "Test", description = "An API for testing the kafka message producer.")
    public ResponseTemplate<?> sendKafkaMessageTest(@RequestParam("id") Long id) {
        LogManager logManager = new LogManager();
        logManager.startClock();

        return otherService.sendKafkaMessageTest(id, logManager);
    }
}
