package et.safaricom.agent_onboarding.model.view.snd;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import lombok.Getter;
import lombok.ToString;
import org.hibernate.annotations.Immutable;
import org.hibernate.annotations.Subselect;

@Getter
@ToString
@Entity
@Immutable
@JsonIgnoreProperties(ignoreUnknown = true)
@Subselect("""
    SELECT status_id, status_desc FROM onboardings.snd_user_status_master
""")
public class SalesUserStatusMaster {
    @Id
    @Column(name = "status_id")
    private Integer statusId;

    @Column(name = "status_desc")
    private String statusDesc;
}
