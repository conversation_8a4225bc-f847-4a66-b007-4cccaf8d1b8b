package et.safaricom.agent_onboarding.repository.view;

import et.safaricom.agent_onboarding.dto.response.query.UserDropDownQueryResponseDto;
import et.safaricom.agent_onboarding.enums.UM_USER_STATUS;
import et.safaricom.agent_onboarding.model.view.InternalUsersEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface InternalUsersEntityRepository extends JpaRepository<InternalUsersEntity, Long> {

    @Query(value = """
        SELECT rsm.first_name as firstName, rsm.middle_name as middleName, rsm.last_name as lastName, 
               rsm.user_name as username, rsm.is_active as isActive, rsm.status as status
        FROM usersandcluster.tbl_internal_user rsm
        WHERE :userId = any(rsm.user_hierarchy)
    """, nativeQuery = true)
    List<UserDropDownProjection> findAllActiveUsersByRoleId(@Param("userId") Long userId);

    @Query("SELECT inter FROM InternalUsersEntity AS inter WHERE inter.userName = :username")
    Optional<InternalUsersEntity> findByUserName(@Param("username") String username);


    @Query("SELECT inter FROM InternalUsersEntity AS inter WHERE inter.parentId = :parentId")
    List<InternalUsersEntity> findAllByParentId(@Param("parentId") Long parentId);

    public interface UserDropDownProjection {
        String getFirstName();
        String getMiddleName();
        String getLastName();
        String getUsername();
        Boolean getIsActive();
        Integer getStatus();
    }
}
