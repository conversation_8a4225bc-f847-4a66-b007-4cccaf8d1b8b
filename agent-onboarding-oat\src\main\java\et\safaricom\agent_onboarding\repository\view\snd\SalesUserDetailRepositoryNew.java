package et.safaricom.agent_onboarding.repository.view.snd;

import et.safaricom.agent_onboarding.model.view.snd.SalesUserDetailNew;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;


    import et.safaricom.agent_onboarding.model.view.snd.SalesUserDetailNew;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

    @Repository
    public interface SalesUserDetailRepositoryNew extends JpaRepository<SalesUserDetailNew, String> {
        Optional<SalesUserDetailNew> findByUserId(String userId);

        List<SalesUserDetailNew> findAllByUserId(String userId);
    }


