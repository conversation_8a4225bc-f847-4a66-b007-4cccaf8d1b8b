package et.safaricom.agent_onboarding.model.view;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import lombok.Getter;
import lombok.ToString;
import org.hibernate.annotations.Immutable;
import org.hibernate.annotations.Subselect;

import java.io.Serializable;

@Getter
@ToString
@Entity
@Immutable
@JsonIgnoreProperties(ignoreUnknown = true)
@Subselect("SELECT tl.id, tl.location_type, tlt.location_type_name, tl.location_name, tl.parent_id, tl.is_active FROM usersandcluster.tbl_locations AS tl INNER JOIN usersandcluster.tbl_location_type as tlt ON tlt.id = tl.location_type")
public class LocationsEntity implements Serializable {
    @Id
    @Column(name = "id")
    private Long id;

    @Column(name = "location_type")
    private Long locationType;

    @Column(name = "location_type_name")
    private String locationTypeName;

    @Column(name = "location_name")
    private String locationName;

    @Column(name = "parent_id")
    private Long parentId;

    @Column(name = "is_active")
    private Boolean isActive;
}
