package et.safaricom.agent_onboarding.utils;

import com.mpesautils.logmanager.Log;
import lombok.NoArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@NoArgsConstructor
public class LoggerCommon {
    public Log getLog(Log log, String logLevel, String transactionId,  int responseCode, String responseMessage, Object detailedMessage) {
        log.setLogLevel(logLevel);
        log.setTransactionID(transactionId);
        log.setResponseCode(String.valueOf(responseCode));
        log.setResponseMessage(responseMessage);
        log.setDetailedMessage("Detailed Message: " + detailedMessage);

        return log;
    }
    public Log getLog(Log log, String logLevel,  int responseCode, String responseMessage, Object detailedMessage) {
        log.setLogLevel(logLevel);

        log.setResponseCode(String.valueOf(responseCode));
        log.setResponseMessage(responseMessage);
        log.setDetailedMessage("Detailed Message: " + detailedMessage);

        return log;
    }

    public Log getLog(Log log, String transactionId,  int responseCode, String responseMessage, Exception e) {
        log.setLogLevel("error");
        log.setTransactionID(transactionId);
        log.setResponseCode(String.valueOf(responseCode));
        log.setResponseMessage(responseMessage);
        log.setDetailedMessage("Exception: " + e.getMessage());

        return log;
    }
    public Log getLog(Log log,  int responseCode, String responseMessage, Exception e) {
        log.setLogLevel("error");
        log.setResponseCode(String.valueOf(responseCode));
        log.setResponseMessage(responseMessage);
        log.setDetailedMessage("Exception: " + e.getMessage());

        return log;
    }



// logManager.error(loggerCommon.getLog(log, "error", 1, "Failed to retrieve the case categories.", response));
}
