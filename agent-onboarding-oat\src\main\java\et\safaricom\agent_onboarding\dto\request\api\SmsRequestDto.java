package et.safaricom.agent_onboarding.dto.request.api;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.validation.annotation.Validated;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SmsRequestDto {
    @JsonProperty("RequestRefID")
    private String RequestRefID;

    @JsonProperty("CommandID")
    private String CommandID;

    @JsonProperty("Sender")
    private Sender Sender;

    @JsonProperty("Receiver")
    private List<Receiver> Receiver;

    @JsonProperty("Content")
    private String Content;

    @JsonProperty("MessageType")
    private String MessageType;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Sender {
        @JsonProperty("name")
        private String name;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Receiver {
        @JsonProperty("id")
        private String id;

    }
}
