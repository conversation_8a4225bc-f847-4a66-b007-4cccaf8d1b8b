package et.safaricom.agent_onboarding.dto.request.kafka;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import et.safaricom.agent_onboarding.enums.KAFKA_PRODUCER_COMMAND_ID;
import et.safaricom.agent_onboarding.enums.USER_STATUS;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

import java.util.List;

@Data
@ToString
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class CreateAgentOnBDKafkaRequestDto {
    @JsonProperty("commandId")
    private KAFKA_PRODUCER_COMMAND_ID commandId;

    @JsonProperty("content")
    private Content content;

    @JsonProperty("isReVerified")
    private Boolean isReVerified;

    @Data
    @ToString
    @SuperBuilder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Content {
        @JsonProperty("nidFirstName")
        private String nidFirstName;

        @JsonProperty("nidMiddleName")
        private String nidMiddleName;

        @JsonProperty("nidLastName")
        private String nidLastName;

        @JsonProperty("crmFullName")
        private String crmFullName;

        @JsonProperty("crmBioMetricsId")
        private String crmBioMetricsId;

        @JsonProperty("agentId")
        private String agentId;

        @JsonProperty("msisdn")
        private String msisdn;

        @JsonProperty("Contact_Number") // Snake Case to be compatible with existing gimpl
        private String contactPhone;

        @JsonProperty("division")
        private String division;

        @JsonProperty("region")
        private String region;

        @JsonProperty("cluster")
        private String cluster;

        @JsonProperty("route")
        private String route;

        @JsonProperty("site")
        private String site;

        @JsonProperty("address")
        private Address address;

        @JsonProperty("rsm")
        private String rsm;

        @JsonProperty("ram")
        private String ram;

        @JsonProperty("updatedOn")
        private String updatedOn;

        @JsonProperty("createdOn")
        private String createdOn;

        @JsonProperty("roles")
        private List<String> roles;

        @JsonProperty("userStatus")
        private USER_STATUS userStatus;

        @Data
        @ToString
        @SuperBuilder
        @NoArgsConstructor
        @AllArgsConstructor
        public static class Address {
            @JsonProperty("kebele")
            private String kebele;

            @JsonProperty("nidRegion")
            private String nidRegion;

            @JsonProperty("woreda")
            private String woreda;

            @JsonProperty("zone")
            private String zone;
        }
    }
}
