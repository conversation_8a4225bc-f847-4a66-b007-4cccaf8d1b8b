package et.safaricom.agent_onboarding.model;

import et.safaricom.agent_onboarding.enums.NID_PICTURE_TYPE;
import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@Embeddable
@NoArgsConstructor
@AllArgsConstructor
public class NIDCardPicture {
    @Column(name = "nid_picture_path")
    private String nidPicturePath;

    @Enumerated(EnumType.STRING)
    @Column(name = "nid_picture_type")
    private NID_PICTURE_TYPE nidPictureType;
}
