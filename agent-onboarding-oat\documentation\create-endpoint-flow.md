# /create Endpoint and initiateBAOnboardingOrReverification Method

## Overview

The `/create` endpoint in the `BAOnboardingController` initiates the Business Agent (BA) onboarding process. This endpoint accepts a `BAOnboardingInitiatorRequestDto` containing the agent's phone number and triggers the `initiateBAOnboardingOrReverification` method with the `INITIATION_TYPE.ONBOARDING` parameter.

## Endpoint Details

```java
@PostMapping("/create")
@Operation(summary = "Initiate BA Onboarding", tags = "BA Onboarding", description = "An API for initiating the BA Onboarding Process")
public ResponseTemplate<?> initiateBAOnboarding(@RequestBody BAOnboardingInitiatorRequestDto requestDto) {
    LogManager logManager = new LogManager();
    logManager.startClock();

    return baOnboardingService.initiateBAOnboardingOrReverification(requestDto, null, INITIATION_TYPE.ONBOARDING, logManager);
}
```

## initiateBAOnboardingOrReverification Method Flow

The `initiateBAOnboardingOrReverification` method in `BAOnboardingService` handles both new agent onboarding and agent reverification processes. Below is a detailed step-by-step explanation of its execution flow:

### 1. Initial Setup

```java
String transactionId = "ONE_PLATFORM_BA_ONBOARDING_" + System.currentTimeMillis();

Log log = new Log(transactionId, "info", "Initiate BA Onboarding", "", "", "", "ONE_PLATFORM_BA_ONBOARDING", "ONE_PLATFORM_BA_ONBOARDING",
        "0", "Default!", "Default Detailed Message.", "");
```

- **Transaction ID Generation**: Creates a unique transaction ID by appending the current timestamp to a prefix.
- **Log Initialization**: Initializes a log object with the transaction ID and default values.

### 2. Authentication and Authorization

```java
String initiatorEmail = helper.extractEmailFromRequest(logManager, log);

ResponseTemplate<UserDetailsResponseDto> userDetailsResponse = commonService.getUserBasicDetails(null, initiatorEmail, internalUsersEntityRepository, externalUsersEntityRepository, logManager, log);

if (userDetailsResponse.isSuccess()) {
    if (Helper.hasAccess(Authorization.CAN_ONBOARD_APP, userDetailsResponse.getData().getUserHierarchies())) {
        // Proceed with onboarding
    } else {
        // Return unauthorized error
    }
}
```

- **Email Extraction**: Extracts the initiator's email from the security context.
- **User Details Retrieval**: Retrieves the user details associated with the email.
- **Permission Check**: Verifies that the user has the `CAN_ONBOARD_APP` permission.

### 3. Phone Number Validation

```java
ResponseTemplate<String> validatedPhoneNumberResponse = helper.validatePhoneNumber(requestDto != null ? requestDto.getPhoneNumber() : re_requestDto.getPhoneNumber());
if (validatedPhoneNumberResponse.isSuccess()) {
    // Proceed with CRM information retrieval
} else {
    // Return validation error
}
```

- **Phone Number Validation**: Validates the phone number from the request.

### 4. CRM Customer Information Retrieval

```java
ResponseTemplate<Map<String, String>> crmUserDetailsResponse = apiGatewayService.crmCustomerInfo(validatedPhoneNumberResponse.getData(), logManager, log);
if (crmUserDetailsResponse.isSuccess()) {
    // Proceed with duplicate check
} else {
    // Return CRM error
}
```

- **CRM API Call**: Calls the CRM system to retrieve customer details based on the phone number.

### 5. Duplicate Check

```java
Optional<BAOnboardingDetails> baOnboardingDetailsOptional = initiatorType.equals(INITIATION_TYPE.ONBOARDING) ? 
    baOnboardingDetailsRepository.findOneByPriority(validatedPhoneNumberResponse.getData(), crmUserDetailsResponse.getData().get("crmBiometricId")) :
    baOnboardingDetailsRepository.findOneByPriority(re_requestDto.getAgentId(), validatedPhoneNumberResponse.getData(), crmUserDetailsResponse.getData().get("crmBiometricId"));

if (baOnboardingDetailsOptional.isPresent()) {
    if (baOnboardingDetailsOptional.get().getStatus().equals(APPLICATION_STATUS.PENDING)
            || baOnboardingDetailsOptional.get().getStatus().equals(APPLICATION_STATUS.APPROVED)
            || baOnboardingDetailsOptional.get().getStatus().equals(APPLICATION_STATUS.REQUIRED)
    ) {
        // Return error for existing user
    } else {
        // Delete existing record with other statuses
        baOnboardingDetailsRepository.delete(baOnboardingDetailsOptional.get());
    }
}
```

- **Duplicate Check**: Checks if an agent with the same phone number or biometric ID already exists.
- **Existing Record Handling**: 
  - If the existing record has status PENDING, APPROVED, or REQUIRED, returns an error.
  - Otherwise, deletes the existing record to allow a fresh onboarding.

### 6. Creating the Onboarding Record

```java
BAOnboardingDetails baOnboardingDetails = BAOnboardingDetails.builder()
        .msisdn(validatedPhoneNumberResponse.getData())
        .crmFirstName(crmUserDetailsResponse.getData().get("firstName"))
        .crmMiddleName(crmUserDetailsResponse.getData().get("middleName"))
        .crmLastName(crmUserDetailsResponse.getData().get("lastName"))
        .crmBiometricsId(crmUserDetailsResponse.getData().get("crmBiometricId"))
        .status(APPLICATION_STATUS.DRAFT)
        .roles(String.join(",", List.of(eKycAgentEKycRole, eKycAgentMPESAEKycRole)))
        .createdBy(initiatorEmail)
        .createdById(userDetailsResponse.getData().getId())
        .build();
```

- **Record Creation**: Creates a new `BAOnboardingDetails` object with:
  - Customer information from CRM
  - Status set to DRAFT
  - eKYC roles assigned
  - Creator information recorded

### 7. Handling Reverification

```java
if (initiatorType == INITIATION_TYPE.RE_VERIFICATION) {
    baOnboardingDetails.setAgentId(re_requestDto.getAgentId());
    baOnboardingDetails.setIsReverified(true);
} else {
    baOnboardingDetails.setIsReverified(false);
}

if (initiatorType.equals(INITIATION_TYPE.RE_VERIFICATION)) {
    baOnboardingDetails.setAgentId(re_requestDto.getAgentId());
}
```

- **Reverification Flag**: If the initiation type is RE_VERIFICATION:
  - Sets the agent ID from the request
  - Sets the isReverified flag to true

### 8. eKYC Last Resort Check

```java
ResponseTemplate<BAOnboardingDetails> ekycLastResortResponse = ekycLastResort(baOnboardingDetails, logManager, log);
if (ekycLastResortResponse.isSuccess()) {
    baOnboardingDetails = ekycLastResortResponse.getData();
    // Save and return response
} else {
    return ekycLastResortResponse;
}
```

- **eKYC Last Resort**: Calls the `ekycLastResort` method which:
  - Performs biometric deduplication using `eKycDeDupHandler` with `EKYC_DEDUB_TYPE.BY_BIOMETRICS_ID`
  - Checks for duplicates in the database
  - Performs vetting using `eKycVetting`
  - Stores additional biometric IDs if found

### 9. Saving and Returning Response

```java
baOnboardingDetails = baOnboardingDetailsRepository.save(baOnboardingDetails);
Map<String, Object> response = new LinkedHashMap<>();
Map<String, Object> responseData = new LinkedHashMap<>();
response.put("id", baOnboardingDetails.getId());
response.put("sessionId", baOnboardingDetails.getSessionId());
Optional<BA_ONBOARDING_APP_PAGES> pageOptional = BA_ONBOARDING_APP_PAGES.getPageByField("msisdn");
response.put("pageId", pageOptional.map(Enum::name).orElse("N/A"));

responseData.put("crmFirstName", baOnboardingDetails.getCrmFirstName());
responseData.put("crmMiddleName", baOnboardingDetails.getCrmMiddleName());
responseData.put("crmLastName", baOnboardingDetails.getCrmLastName());
responseData.put("msisdn", baOnboardingDetails.getMsisdn());
response.put("data", responseData);

return ResponseTemplate.success(response);
```

- **Save Record**: Saves the `BAOnboardingDetails` to the database.
- **Response Preparation**: Creates a response with:
  - Record ID
  - Session ID
  - Next page ID
  - Basic CRM customer information

## ekycLastResort Method

The `ekycLastResort` method performs critical eKYC checks:

```java
private ResponseTemplate<BAOnboardingDetails> ekycLastResort(BAOnboardingDetails updateBAOnboardingDetails, LogManager logManager, Log log) {
    ResponseTemplate<List<String>> eKycDeDupHandlerResponse = apiGatewayService.eKycDeDupHandler(new EKycVerificationApiRequestDto(), updateBAOnboardingDetails.getCrmBiometricsId(), EKYC_DEDUB_TYPE.BY_BIOMETRICS_ID, logManager, log);
    if (eKycDeDupHandlerResponse.isSuccess()) {
        List<BAOnboardingDetails> findAllByBiometricsIdList = baOnboardingDetailsRepository.findAllByBiometricsIdList(eKycDeDupHandlerResponse.getData());
        if (findAllByBiometricsIdList.isEmpty() || findAllByBiometricsIdList.stream().filter(ba -> !ba.getStatus().equals(APPLICATION_STATUS.DRAFT)).toList().isEmpty()) {
            ResponseTemplate<?> eKycVettingResponse = apiGatewayService.eKycVetting(updateBAOnboardingDetails.getCrmBiometricsId(), logManager, log);
            if (eKycVettingResponse.isSuccess()) {
                if (eKycDeDupHandlerResponse.getData() != null) {
                    if (updateBAOnboardingDetails.getBiometricsIdList() == null) {
                        updateBAOnboardingDetails.setBiometricsIdList(new ArrayList<>());
                    }
                    updateBAOnboardingDetails.getBiometricsIdList().addAll(eKycDeDupHandlerResponse.getData());
                }
                return ResponseTemplate.success(updateBAOnboardingDetails);
            } else {
                return ResponseTemplate.error("Vetting failed!");
            }
        } else {
            return ResponseTemplate.error("User already exists!");
        }
    } else {
        return ResponseTemplate.error(eKycDeDupHandlerResponse.getMessage());
    }
}
```

1. **Biometric ID Deduplication**: Calls `eKycDeDupHandler` with `EKYC_DEDUB_TYPE.BY_BIOMETRICS_ID` to check for duplicate biometric IDs.
2. **Database Check**: Queries the database for any existing records with the same biometric IDs.
3. **Vetting**: If no duplicates are found (or only DRAFT duplicates exist), performs vetting through `eKycVetting`.
4. **Biometric ID Storage**: Stores any additional biometric IDs returned from the deduplication process.

## Error Handling

The method includes comprehensive error handling at each step:

1. **Authorization Errors**: Returns "Unauthorized!" if the user lacks proper permissions.
2. **Validation Errors**: Returns validation errors for invalid phone numbers.
3. **CRM Errors**: Returns errors from CRM API calls.
4. **Duplicate Errors**: Returns "User already exist!" for duplicate records.
5. **eKYC Errors**: Returns specific errors from eKYC operations (verification, deduplication, vetting).

## Summary

The `/create` endpoint and `initiateBAOnboardingOrReverification` method implement a robust process for onboarding new business agents:

1. Validates the initiator's permissions
2. Validates the phone number
3. Retrieves customer information from CRM
4. Checks for duplicates
5. Creates a new onboarding record
6. Performs eKYC checks (deduplication and vetting)
7. Saves the record and returns a success response

This process ensures that only authorized users can initiate onboarding, prevents duplicate agents, and integrates with external systems (CRM and eKYC) for comprehensive verification.