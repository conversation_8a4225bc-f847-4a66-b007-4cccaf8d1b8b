package et.safaricom.agent_onboarding.geo.repository;

import et.safaricom.agent_onboarding.geo.view.ClustersLDB;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.Set;

@Repository
public interface ClustersLDBRepository extends JpaRepository<ClustersLDB, String> {

    @Query("SELECT cl FROM ClustersLDB AS cl WHERE ST_Contains(cl.geom, ST_SetSRID(ST_MakePoint(:longitude, :latitude), 4326))")
    Optional<ClustersLDB> findByCoordinates(@Param("longitude") Double longitude, @Param("latitude") Double latitude);

    @Query("SELECT cl FROM ClustersLDB AS cl WHERE cl.clusterID in :clusterIdList")
    List<ClustersLDB> findAllByClusterIdIn(@Param("clusterIdList") List<String> clusterIdList);

    @Query("SELECT DISTINCT cl.cluster FROM ClustersLDB AS cl WHERE cl.ramBaseCity = :ramBaseCity")
    Set<String> findAllClustersByRegion(@Param("ramBaseCity") String region);

    @Query("SELECT DISTINCT cl.ramBaseCity FROM ClustersLDB AS cl WHERE cl.nsmDivision = :nsmDivision")
    Set<String> findAllRegionsByDivision(@Param("nsmDivision") String division);

    @Query("SELECT DISTINCT cl.nsmDivision FROM ClustersLDB AS cl")
    Set<String> findAllDivisions();
}
