package et.safaricom.agent_onboarding.Logging;

import org.slf4j.MDC;

import static et.safaricom.agent_onboarding.Logging.MdcKeys.*;


public final class LoggingHelper {
    private LoggingHelper() {}


    public static void setTransactionId(String transactionId) {
        if (transactionId != null) {
            MDC.put(TRANSACTION_ID, transactionId);
        }
    }

    public static void setResponseCode(String code){
        if(code!=null){
            MDC.put(RESPONSE_CODE, code);
        }
    }
    public static void setOutcome(String outcome){
        if(outcome!=null){
            MDC.put(OUTCOME, outcome);
        }
    }
    public static void setProcess(String process){
        if(process!=null){
            MDC.put(PROCESS_NAME, process);
        }
    }



}
