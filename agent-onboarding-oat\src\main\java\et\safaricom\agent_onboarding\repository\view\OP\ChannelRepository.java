package et.safaricom.agent_onboarding.repository.view.OP;

import et.safaricom.agent_onboarding.model.view.OP.TblChannelView;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface ChannelRepository extends JpaRepository<TblChannelView, Long> {
    @Query("SELECT ch.shortCode FROM TblChannelView ch WHERE ch.parentId = :parentId")
    Optional<String> getDistributorShortCodeByDistributorId(@Param("parentId") Long distributorId);
}

