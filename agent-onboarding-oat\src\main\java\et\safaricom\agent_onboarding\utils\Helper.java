package et.safaricom.agent_onboarding.utils;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.mpesautils.logmanager.Log;
import com.mpesautils.logmanager.LogManager;
import et.safaricom.agent_onboarding.dto.request.BAOnboardingUpdateRequestDto;
import et.safaricom.agent_onboarding.enums.NID_PICTURE_TYPE;
import et.safaricom.agent_onboarding.enums.REQUIRED_FILE_TYPE;
import et.safaricom.agent_onboarding.model.BAOnboardingDetails;
import et.safaricom.agent_onboarding.repository.BAOnboardingDetailsRepository;
import et.safaricom.agent_onboarding.service.FileService;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.experimental.SuperBuilder;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ThreadLocalRandom;
import java.util.stream.Stream;


@Component
@RequiredArgsConstructor
public class Helper {
    private final LoggerCommon loggerCommon;

    public static Map<String, Object> getPageableResponse(Page<?> pageableData) {
        Map<String, Object> response = new HashMap<>();
        response.put("pageSize", pageableData.getSize());
        response.put("currentPage", pageableData.getNumber() + 1);
        response.put("totalElements", pageableData.getTotalElements());
        response.put("totalPages", pageableData.getTotalPages());

        return response;
    }

    public String extractEmailFromRequest(LogManager logManager, Log log , String transactionId) {
        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            if (authentication != null && authentication.getPrincipal() instanceof Jwt jwt) {
                return jwt.getClaim("preferred_username").toString().toLowerCase();
            } else if (authentication != null && authentication.getPrincipal() instanceof String) {
                return ((String) authentication.getPrincipal()).toLowerCase();
            } else {
                throw new IllegalArgumentException("Unsupported principal type: " + (authentication != null ? authentication.getPrincipal().getClass().getName() : "null"));
            }
        } catch (Exception e) {
            e.printStackTrace();
            logManager.error(loggerCommon.getLog(log, transactionId , 1, "Error occurred while extracting email from request", e));
            return null;
        }
    }

    public ResponseTemplate<String> validatePhoneNumber(String phoneNumber) {
//        ------------------------------- Only Safaricom Line is supported. -------------------------------
        if (phoneNumber.length() == 13 && phoneNumber.startsWith("+2517")) { // If it's starting with +2517
            return new ResponseTemplate<>(true, "", 0, phoneNumber.substring(1));
        } else if (phoneNumber.length() == 12 && phoneNumber.startsWith("2517")) { // If it's starting with 2517
            return new ResponseTemplate<>(true, "", 0, phoneNumber);
        } else if (phoneNumber.length() == 10 && phoneNumber.startsWith("07")) { // If it's starting with 07
            return new ResponseTemplate<>(true, "", 0, "251" + phoneNumber.substring(1));
        } else if (phoneNumber.length() == 9 && phoneNumber.startsWith("7")) { // If it's starting with 7
            return new ResponseTemplate<>(true, "", 0, "251" + phoneNumber);
        } else { // invalid
            return new ResponseTemplate<>(false, "Invalid phone number (" + phoneNumber + ")", 1, null);
        }
    }

    public static <T> Map<String, Object> convertDtoToMap(T dto) {
        Map<String, Object> resultMap = new HashMap<>();
        if (dto == null) {
            return resultMap;
        }

        Field[] fields = dto.getClass().getDeclaredFields();

        for (Field field : fields) {
            field.setAccessible(true);
            try {
                Object value = field.get(dto);
                if (value != null) {
                    resultMap.put(field.getName(), value);
                }
            } catch (IllegalAccessException e) {
                throw new RuntimeException("Failed to access field: " + field.getName(), e);
            }
        }

        return resultMap;
    }

    public static String getCamelCaseKey(String kycKey, boolean toCamelCase) {
        String newKey = kycKey.split("\\]\\[", 3)[2].replace("]", "");
        if (newKey.equals("Sales Region")) {
            newKey = kycKey.split("\\]\\[", 3)[1].replace("]", "") + newKey;
        }

        if (!newKey.isEmpty()) {
            if (toCamelCase) {
                newKey = newKey.replace(" ", "");
                newKey = Character.toLowerCase(newKey.charAt(0)) + newKey.substring(1);
            }
        }
        return newKey;
    }

    public static Map<String, Object> keyValueCheck(Map<String, Object> body, List<String> keys) {
        if (body != null) {
            for (String key : keys) {
                Object value = body.get(key);
                if (value == null || (value instanceof String && ((String) value).trim().isEmpty())) {
                    return Map.of(
                            "isError", true,
                            "message", key + " must be specified!"
                    );
                }
            }
        }
        else {
            return Map.of(
                    "isError", true,
                    "message", "Mandatory fields are missing!"
            );
        }
        return Map.of("isError", false);
    }

    public static String validateStatusForAppResponse(String status) {
        if (status != null && !status.isBlank()) {
            return switch (status) {
                case "1", "01" -> "CLOSED";
                case "3", "03" -> "IN-PROGRESS";
                case "4", "04" -> "OPEN";
                case "5", "05" -> "RE-OPEN";
                case "7", "07" -> "RESOLVED";
                default -> "UNKNOWN";
            };
        } else {
            return status;
        }
    }

    public static String formatFullName(String firstName, String middleName, String lastName) {
        return Stream.of(firstName, middleName, lastName)
                .filter(name -> name != null && !name.trim().isEmpty())
                .map(String::trim)
                .reduce((a, b) -> a + " " + b)
                .orElse("N/A");
    }

    public static boolean hasAccess(Long[] authorizedRoles, List<Long> userHierarchyIds) {
        return userHierarchyIds.stream()
                .anyMatch(id -> Arrays.asList(authorizedRoles).contains(id));
    }

    /**
     * Extracts non-null and non-empty field names from the given object, including nested objects.
     * Excludes fields specified in the `excludedFields` list.
     *
     * @param obj           The object to extract fields from.
     * @param excludedFields A list of field names to exclude.
     * @return List of non-empty field names.
     */
    public static List<String> extractNonEmptyFields(Object obj, List<String> excludedFields) {
        if (obj == null) {
            return Collections.emptyList();
        }

        List<String> result = new ArrayList<>();
        extractFields(obj, "", result, new HashSet<>(excludedFields));
        return result;
    }

    /**
     * Recursively extracts non-null and non-empty field names from the object.
     */
    public static void extractFields(Object obj, String parentField, List<String> result, Set<String> excludedFields) {
        if (obj == null) return;

        Field[] fields = obj.getClass().getDeclaredFields();
        for (Field field : fields) {
            field.setAccessible(true);
            try {
                Object value = field.get(obj);
                String fieldName = parentField.isEmpty() ? field.getName() : parentField + "." + field.getName();

                if (excludedFields.contains(field.getName())) {
                    continue;
                }

                if (value != null) {
                    if (value instanceof String strValue) {
                        if (!strValue.trim().isEmpty()) {
                            result.add(fieldName);
                        }
                    } else if (value instanceof Number || value instanceof Boolean || value instanceof Enum<?>) {
                        result.add(fieldName);
                    } else if (value instanceof LocalDate || value instanceof LocalDateTime) {
                        result.add(fieldName);
                    } else if (value instanceof Collection<?> collection) {
                        if (!collection.isEmpty()) {
                            result.add(fieldName);
                        }
                    } else if (value instanceof Map<?, ?> map) {
                        if (!map.isEmpty()) {
                            result.add(fieldName);
                        }
                    } else {
                        extractFields(value, fieldName, result, excludedFields);
                    }
                }
            } catch (IllegalAccessException ignored) {
            }
        }
    }

    public static String identifyRequestSource(String userAgent) {
        if (userAgent == null || userAgent.isEmpty()) {
            return "UNKNOWN";
        }

        String lowerUserAgent = userAgent.toLowerCase();

        if (lowerUserAgent.contains("okhttp") || lowerUserAgent.contains("dalvik") || lowerUserAgent.contains("mobile") ||
                lowerUserAgent.contains("android") || lowerUserAgent.contains("iphone") || lowerUserAgent.contains("ios")) {
            return "MOBILE_APP";
        }

        if (lowerUserAgent.contains("chrome") || lowerUserAgent.contains("firefox") ||
                lowerUserAgent.contains("safari") || lowerUserAgent.contains("edge") ||
                lowerUserAgent.contains("mozilla")) {
            return "WEB";
        }

        return "UNKNOWN";
    }

    public ResponseTemplate<?> validateNIDCardPictures(BAOnboardingUpdateRequestDto requestDto, FileService fileService, LogManager logManager, Log log) {
        List<BAOnboardingUpdateRequestDto.NIDCardPicture> nidCardPictureList = requestDto.getNidCardPictureList();

        if (nidCardPictureList == null || nidCardPictureList.isEmpty()) {
            return ResponseTemplate.error("NID card pictures are required.");
        }

        boolean hasFront = false;
        boolean hasRare = false;

        for (BAOnboardingUpdateRequestDto.NIDCardPicture picture : nidCardPictureList) {
            if (picture.getNidCardPictureType().equals(NID_PICTURE_TYPE.FRONT)) {
                hasFront = true;
            } else if (picture.getNidCardPictureType().equals(NID_PICTURE_TYPE.RARE)) {
                hasRare = true;
            }

            if (picture.getNidCardPicturePath() == null || picture.getNidCardPicturePath().isBlank()) {
                return ResponseTemplate.error("Invalid NID card picture path.");
            }

            ResponseEntity<?> fileResponse = fileService.getFile(picture.getNidCardPicturePath(), REQUIRED_FILE_TYPE.BASE64, logManager);
            if (!fileResponse.getStatusCode().is2xxSuccessful()) {
                loggerCommon.getLog(log, "info", log.getTransactionID(), 1, "Please retake the pictures again!", "NID card picture file does not exist: " + picture.getNidCardPicturePath());
                return ResponseTemplate.error("Please retake the pictures again!");
            }
        }

        if (!hasFront || !hasRare) {
            return ResponseTemplate.error("Both FRONT and RARE NID card pictures are required.");
        }

        return ResponseTemplate.success("Validation successful!");
    }

    public static Map<String, String> parseFullName(String fullName) {
        Map<String, String> nameMap = new LinkedHashMap<>();
        if (fullName == null || fullName.trim().isEmpty()) {
            throw new IllegalArgumentException("Full name cannot be null or empty.");
        }

        String[] parts = fullName.trim().split("\\s+");

        if (parts.length == 1) {
            nameMap.put("firstName", parts[0]);
            nameMap.put("middleName", "");
            nameMap.put("lastName", "");
        } else if (parts.length == 2) {
            nameMap.put("firstName", parts[0]);
            nameMap.put("middleName", "");
            nameMap.put("lastName", parts[1]);
        } else {
            nameMap.put("firstName", parts[0]);
            nameMap.put("middleName", String.join(" ", java.util.Arrays.copyOfRange(parts, 1, parts.length - 1)));
            nameMap.put("lastName", parts[parts.length - 1]);
        }

        return nameMap;
    }

    public static int getRandomNumber() {
        return ThreadLocalRandom.current().nextInt(1, 50);
    }

    public static String generateAgentId(String firstName, String lastName) {
        if (firstName == null || firstName.isEmpty() || lastName == null || lastName.isEmpty()) {
            throw new IllegalArgumentException("Both strings must be non-empty");
        }

        char firstChar = Character.toLowerCase(firstName.charAt(0));
//        String capitalizedLastName = Character.toUpperCase(lastName.charAt(0)) + lastName.substring(1).toLowerCase();
        return firstChar + "." + lastName.toLowerCase().trim()+ "_"+System.nanoTime() % 1000000 + UUID.randomUUID().toString().substring(0, 2);
//         e.tesfa_89012a1
  //      return firstChar + "." + lastName.toLowerCase().trim();
    }

    public static Object getFieldValue(BAOnboardingDetails baOnboardingDetails, String fieldName) {
        try {
            Field field = BAOnboardingDetails.class.getDeclaredField(fieldName);
            field.setAccessible(true);
            return field.get(baOnboardingDetails);
        } catch (NoSuchFieldException | IllegalAccessException e) {
            return null;
        }
    }

    public static double round(double value, int places) {
        double scale = Math.pow(10, places);
        return Math.round(value * scale) / scale;
    }

    public static Map<String, Integer> processGeoLocationResults(List<BAOnboardingDetailsRepository.CountByLocationDto> results) {
        Map<String, Integer> map = new HashMap<>();
        for (BAOnboardingDetailsRepository.CountByLocationDto row : results) {
            map.put((String) row.getLocation(), row.getTotalUsers());
        }
        return map;
    }

    public static LocalDateTime convertFromToSunRises(LocalDate date) {
        return date.atStartOfDay();
    }

    public static LocalDateTime convertToToSunSets(LocalDate date) {
        return date.atTime(23, 59, 59, 999999999);
    }

    public String extractEmailFromToken(String jwtToken) {
        try {
            // JWT format: header.payload.signature
            String[] chunks = jwtToken.split("\\.");
            if (chunks.length < 2) {
                throw new IllegalArgumentException("Invalid JWT token");
            }

            // Decode payload (second part)
            String payload = new String(Base64.getUrlDecoder().decode(chunks[0]));

            // Convert JSON string to Map
            ObjectMapper objectMapper = new ObjectMapper();
            Map<String, Object> claims = objectMapper.readValue(payload, Map.class);

            // Keycloak usually stores email under "email"
            return (String) claims.get("email");
        } catch (Exception e) {
            throw new RuntimeException("Failed to extract email from token", e);
        }
    }
}
