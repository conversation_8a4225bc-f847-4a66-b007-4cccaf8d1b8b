package et.safaricom.agent_onboarding.dto.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import et.safaricom.agent_onboarding.enums.APPLICATION_STATUS;
import et.safaricom.agent_onboarding.enums.NID_PICTURE_TYPE;
import et.safaricom.agent_onboarding.enums.USER_STATUS;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Data
@ToString
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class AgentDetailsWebResponseDto {
    @JsonProperty("id")
    private Long id;

    @JsonProperty("nidFirstName")
    private String nidFirstName;

    @JsonProperty("nidMiddleName")
    private String nidMiddleName;

    @JsonProperty("nidLastName")
    private String nidLastName;

    @JsonProperty("nidDateOfBirth")
    private LocalDate nidDateOfBirth;

    @JsonProperty("crmFullName")
    private String crmFullName;

    @JsonProperty("status")
    private APPLICATION_STATUS status;

    @JsonProperty("reasonForApplicationStatusUpdate")
    private String reasonForApplicationStatusUpdate;

    @JsonProperty("userStatus")
    private USER_STATUS userStatus;

    @JsonProperty("reasonForUserUpdate")
    private String reasonForUserUpdate;

    @JsonProperty("agentId")
    private String agentId;

    @JsonProperty("faydaNumber")
    private String faydaNumber;

    @JsonProperty("msisdn")
    private String msisdn;

    @JsonProperty("distributorId")
    private Long distributorId;

    @JsonProperty("distributor")
    private String distributor;

    @JsonProperty("distributorShopId")
    private Long distributorShopId;

    @JsonProperty("distributorShop")
    private String distributorShop;

    @JsonProperty("createdBy")
    private String createdBy;

    @JsonProperty("createdOn")
    private LocalDateTime createdOn;

    @JsonProperty("division")
    private String division;

    @JsonProperty("region")
    private String region;

    @JsonProperty("cluster")
    private String cluster;

    @JsonProperty("route")
    private String route;

    @JsonProperty("site")
    private String site;

    @JsonProperty("approvedBy")
    private String approvedBy;

    @JsonProperty("approvedOn")
    private LocalDateTime approvedOn;

    @JsonProperty("address")
    private Address address;

    @JsonProperty("latitude")
    private Double latitude;

    @JsonProperty("longitude")
    private Double longitude;

    @JsonProperty("updatedBy")
    private String updatedBy;

    @JsonProperty("updatedOn")
    private String updatedOn;

    @JsonProperty("letterOfConsentList")
    private List<String> letterOfConsentList = new ArrayList<>();

    @JsonProperty("nidCardPictureList")
    private List<NIDCardPicture> nidCardPictureList = new ArrayList<>();

    @JsonProperty("nidIDExpirationDate")
    private LocalDate nidIDExpirationDate;

    @JsonProperty("nidPhotoPath")
    private String nidPhotoPath;

    @JsonProperty("agentPhotoList")
    private List<String> agentPhotoList = new ArrayList<>();

    @JsonProperty("rsm")
    private String rsm;

    @JsonProperty("ram")
    private String ram;

    @JsonProperty("isAgentCreatedOnEkyc")
    private Boolean isAgentCreatedOnEkyc;

    @JsonProperty("isAgentCreatedOnDxl")
    private Boolean isAgentCreatedOnDxl;

    @JsonProperty("isAgentCreatedOnBD")
    private Boolean isAgentCreatedOnBD;

    @JsonProperty("reverifiedBy")
    private String reverifiedBy;



    @JsonProperty("isNew")
    private Boolean isNew;


    @Data
    @ToString
    @SuperBuilder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Address {
        @JsonProperty("kebele")
        private String kebele;

        @JsonProperty("nidRegion")
        private String nidRegion;

        @JsonProperty("woreda")
        private String woreda;

        @JsonProperty("zone")
        private String zone;
    }

    @Data
    @ToString
    @SuperBuilder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class NIDCardPicture {
        @JsonProperty("nidPicturePath")
        private String nidPicturePath;

        @JsonProperty("nidPictureType")
        private NID_PICTURE_TYPE nidPictureType;

    }
}
