package et.safaricom.agent_onboarding.dto.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import et.safaricom.agent_onboarding.enums.APPLICATION_STATUS;
import et.safaricom.agent_onboarding.enums.USER_STATUS;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

import java.time.LocalDateTime;

@Data
@ToString
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class AgentDetailsAppResponseDto {
    @JsonProperty("id")
    private Long id;

    @JsonProperty("nidFullName")
    private String nidFullName;

    @JsonProperty("status")
    private APPLICATION_STATUS status;

    @JsonProperty("userStatus")
    private USER_STATUS userStatus;

    @JsonProperty("agentId")
    private String agentId;

    @JsonProperty("faydaNumber")
    private String faydaNumber;

    @JsonProperty("msisdn")
    private String msisdn;

    @JsonProperty("distributorId")
    private Long distributorId;

    @JsonProperty("distributor")
    private String distributor;

    @JsonProperty("distributorShopId")
    private Long distributorShopId;

    @JsonProperty("distributorShop")
    private String distributorShop;

    @JsonProperty("createdOn")
    private LocalDateTime createdOn;

    @JsonProperty("region")
    private String region;

    @JsonProperty("cluster")
    private String cluster;

    @JsonProperty("address")
    private AgentDetailsWebResponseDto.Address address;

    @JsonProperty("latitude")
    private Double latitude;

    @JsonProperty("longitude")
    private Double longitude;


    @JsonProperty("rsm")
    private String rsm;

    @JsonProperty("isNew")
    private Boolean isNew;

    @Data
    @ToString
    @SuperBuilder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Address {
        @JsonProperty("kebele")
        private String kebele;

        @JsonProperty("nidRegion")
        private String nidRegion;

        @JsonProperty("woreda")
        private String woreda;

        @JsonProperty("zone")
        private String zone;
    }
}
