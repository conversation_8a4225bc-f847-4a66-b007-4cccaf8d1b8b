package et.safaricom.agent_onboarding.geo.repository;

import et.safaricom.agent_onboarding.geo.view.MasterLocation;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Set;

@Repository
public interface MasterLocationRepository extends JpaRepository<MasterLocation, Integer> {

    @Query("SELECT DISTINCT CAST(ml.siteId AS string) FROM MasterLocation AS ml WHERE ml.distClusterName = :distClusterName")
    Set<String> findAllSiteIdByDistClusterName(@Param("distClusterName") String distClusterName);
}
