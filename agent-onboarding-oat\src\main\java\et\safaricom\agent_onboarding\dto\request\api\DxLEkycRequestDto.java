package et.safaricom.agent_onboarding.dto.request.api;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DxLEkycRequestDto {
    @JsonProperty("RequestRefID")
    private String requestRefID;

    @JsonProperty("CommandID")
    private String commandID;

    @JsonProperty("Remark")
    private String remark;

    @JsonProperty("ChannelSessionID")
    private String channelSessionID;

    @JsonProperty("SourceSystem")
    private String sourceSystem;

    @JsonProperty("Version")
    private String version;

    @JsonProperty("Timestamp")
    private String timestamp;

    @JsonProperty("Parameters")
    private List<Parameter> parameters;

    @JsonProperty("ReferenceData")
    private List<ReferenceData> referenceData;

    @JsonProperty("Initiator")
    private Initiator initiator;

    @JsonProperty("ReceiverParty")
    private ReceiverParty receiverParty;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Parameter {

        @JsonProperty("Key")
        private String key;

        @JsonProperty("Value")
        private String value;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ReferenceData {

        @JsonProperty("Key")
        private String key;

        @JsonProperty("Value")
        private String value;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Initiator {

        @JsonProperty("IdentifierType")
        private String identifierType;

        @JsonProperty("Identifier")
        private String identifier;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ReceiverParty {

        @JsonProperty("IdentifierType")
        private int identifierType;

        @JsonProperty("Identifier")
        private String identifier;
    }
}
