package et.safaricom.agent_onboarding.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.mpesautils.logmanager.Log;
import com.mpesautils.logmanager.LogManager;
import et.safaricom.agent_onboarding.repository.DealerInfoRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import et.safaricom.agent_onboarding.Logging.LoggingHelper;
import et.safaricom.agent_onboarding.dto.request.*;
import et.safaricom.agent_onboarding.dto.request.api.*;
import et.safaricom.agent_onboarding.dto.request.filter.ListUsersFilterParamsRequestDto;
import et.safaricom.agent_onboarding.dto.request.kafka.CreateAgentOnBDKafkaRequestDto;
import et.safaricom.agent_onboarding.dto.response.*;
import et.safaricom.agent_onboarding.dto.response.commonResponse.SNDAndOPResponse;
import et.safaricom.agent_onboarding.enums.*;
import et.safaricom.agent_onboarding.model.*;
import et.safaricom.agent_onboarding.model.view.ChannelsEntity;
import et.safaricom.agent_onboarding.model.view.ExternalUsersEntity;
import et.safaricom.agent_onboarding.model.view.InternalUsersEntity;
import et.safaricom.agent_onboarding.model.view.snd.SalesChannelMaster;
import et.safaricom.agent_onboarding.model.view.snd.SalesUserDetail;
import et.safaricom.agent_onboarding.model.view.snd.SalesUserDetailNew;
import et.safaricom.agent_onboarding.model.view.snd.SalesUserStatusMaster;
import et.safaricom.agent_onboarding.repository.BAOnboardingDetailsRepository;
import et.safaricom.agent_onboarding.repository.view.ChannelsEntityRepository;
import et.safaricom.agent_onboarding.repository.view.ExternalUsersEntityRepository;
import et.safaricom.agent_onboarding.repository.view.InternalUsersEntityRepository;
import et.safaricom.agent_onboarding.repository.view.OP.ChannelRepository;
import et.safaricom.agent_onboarding.repository.view.snd.SalesChannelMasterRepository;
import et.safaricom.agent_onboarding.repository.view.snd.SalesUserDetailRepository;
import et.safaricom.agent_onboarding.repository.view.snd.SalesUserDetailRepositoryNew;
import et.safaricom.agent_onboarding.model.view.snd.SalesUserDetailNew;
import et.safaricom.agent_onboarding.repository.view.snd.SalesUserStatusMasterRepository;
import et.safaricom.agent_onboarding.utils.*;
import lombok.RequiredArgsConstructor;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.PDPage;
import org.apache.pdfbox.pdmodel.PDPageContentStream;
import org.apache.pdfbox.pdmodel.common.PDRectangle;
import org.apache.pdfbox.pdmodel.graphics.image.LosslessFactory;
import org.apache.pdfbox.pdmodel.graphics.image.PDImageXObject;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.lang.reflect.Field;
import java.text.MessageFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;


@Service
@RequiredArgsConstructor
public class BAOnboardingService {

    private static final Logger logger = LoggerFactory.getLogger(BAOnboardingService.class);

    private final Helper helper;
    private final FileService fileService;
    private final LoggerCommon loggerCommon;
    private final CommonService commonService;
    private final APIGatewayService apiGatewayService;
    private final KafkaProducerService kafkaProducerService;

    private final BAOnboardingDetailsRepository baOnboardingDetailsRepository;
    private final InternalUsersEntityRepository internalUsersEntityRepository;
    private final ExternalUsersEntityRepository externalUsersEntityRepository;
    private final ChannelsEntityRepository channelsEntityRepository;
    private final SalesUserDetailRepository salesUserDetailRepository;
    private final SalesUserStatusMasterRepository salesUserStatusMasterRepository;
    private final SalesChannelMasterRepository salesChannelMasterRepository;
    private final SalesUserDetailRepositoryNew salesUserDetailRepositoryNew;


    private final ChannelRepository channelRepository;
    private final DealerInfoRepository dealerInfoRepository;
    private final RestTemplate restTemplate = new RestTemplate();


    @Value("${sms.ba-onboarding.application-status-update.template}")
    private String applicationStatusUpdateSMSContent;

    @Value("${sms.ba-onboarding.notify-adjudicator.template}")
    private String adjudicatorNotificationSMSContent;

    @Value("${sms.ba-onboarding.agent-application-status-approved.template}")
    private String agentApplicationStatusApprovedSMSContent;

    @Value("${sms.reverified.ba-onboarding.application-status-update.template}")
    private String applicationStatusUpdatedReverifiedSMSContent;

    @Value("${sms.reverified.ba-onboarding.notify-adjudicator.template}")
    private String adjudicatorNotificationReverifiedSMSContent;

    @Value("${sms.reverified.ba-onboarding.agent-application-status-approved.template}")
    private String agentApplicationStatusReverificationSMSContent;

    @Value("${ekyc.create-agent.role}")
    private String eKycAgentEKycRole;

    @Value("${ekyc.create-agent.mpesa-role}")
    private String eKycAgentMPESAEKycRole;

    private static List<String> reverseList(List<String> originalList) {
        if (originalList == null || originalList.isEmpty()) {
            return new ArrayList<>();
        }
        List<String> reversedList = new ArrayList<>(originalList);
        Collections.reverse(reversedList);
        return reversedList;
    }

    @Value("${snd.app.keycloak.token-url}")
    private String tokenUrl;

    @Value("${snd.app.partner.details-url}")
    private String partnerUrl;

    @Value("${auth.client-id}")
    private String clientId;

    @Value("${auth.client-secret}")
    private String clientSecret;

    @Value("${auth.scope}")
    private String scope;

    @Value("${auth.grant-type}")
    private String grantType;

    public ResponseTemplate<?> initiateBAOnboardingOrReverification(BAOnboardingInitiatorRequestDto requestDto, BAReverificationInitiatorRequestDto re_requestDto, INITIATION_TYPE initiatorType, LogManager logManager) {
        String transactionId = "ONE_PLATFORM_BA_ONBOARDING_" + UUID.randomUUID() + System.currentTimeMillis();

        LoggingHelper.setTransactionId(transactionId);
        LoggingHelper.setProcess("INITIATE_BA_REQUEST");
        LoggingHelper.setOutcome("START");
        logger.info("business.event=INITIATE_BA_REQUEST_START INITIATION_TYPE={}  transactionId={} httpStatus={}",  initiatorType, transactionId, 100);
        Log log = new Log(transactionId, "info", "Initiate BA Onboarding", "", "", "", "ONE_PLATFORM_BA_ONBOARDING", "ONE_PLATFORM_BA_ONBOARDING",
                "0", "Default!", "Default Detailed Message.", "");

        try {
            String initiatorEmail = helper.extractEmailFromRequest(logManager, log , transactionId );
            LoggingHelper.setOutcome("EMAIL_EXTRACTED");
            logger.info("business.event=INITIATE_BA_EMAIL_EXTRACTED INITIATION_TYPE={}  transactionId={}", initiatorType, transactionId);
            ResponseTemplate<UserDetailsResponseDto> userDetailsResponse = commonService.getUserBasicDetails(null, initiatorEmail, internalUsersEntityRepository, externalUsersEntityRepository, logManager, log);
            logger.info("business.event=INITIATE_BA_USER_DETAILS_FETCHED INITIATION_TYPE={} transactionId={} success={}", initiatorType, transactionId, userDetailsResponse.isSuccess());

            if (userDetailsResponse.isSuccess()) {
                if (Helper.hasAccess(Authorization.CAN_ONBOARD_APP, userDetailsResponse.getData().getUserHierarchies())) {
                    ResponseTemplate<String> validatedPhoneNumberResponse = helper.validatePhoneNumber(requestDto != null ? requestDto.getPhoneNumber() : re_requestDto.getPhoneNumber());
                    if (validatedPhoneNumberResponse.isSuccess()) {

                        LoggingHelper.setTransactionId(transactionId);LoggingHelper.setProcess("PHONE_NUMBER_VALIDATION");LoggingHelper.setOutcome("SUCCESS");
                        logger.info("business.event=VALIDATION  INITIATION_TYPE={}  transactionId={} ",  initiatorType, transactionId);
                        ResponseTemplate<Map<String, String>> crmUserDetailsResponse = apiGatewayService.crmCustomerInfo(validatedPhoneNumberResponse.getData(), logManager, log);

                        if (crmUserDetailsResponse.isSuccess()) {
                            LoggingHelper.setTransactionId(transactionId);LoggingHelper.setProcess("CRM_RESPONSE");LoggingHelper.setOutcome("SUCCESS"); LoggingHelper.setResponseCode("200");
                            logger.info("business.event=VALIDATION  INITIATION_TYPE={}  transactionId={}",  initiatorType, transactionId);

//                            Optional<BAOnboardingDetails> baOnboardingDetailsOptional = initiatorType.equals(INITIATION_TYPE.ONBOARDING) ? baOnboardingDetailsRepository.findByMsisdnOrCrmBiometricsId(validatedPhoneNumberResponse.getData(), crmUserDetailsResponse.getData().get("crmBiometricId")) :
//                                    baOnboardingDetailsRepository.findByMsisdnOrCrmBiometricsIdOrAgentId(validatedPhoneNumberResponse.getData(), crmUserDetailsResponse.getData().get("crmBiometricId"), re_requestDto.getAgentId());

                            Optional<BAOnboardingDetails> baOnboardingDetailsOptional = initiatorType.equals(INITIATION_TYPE.ONBOARDING) ? baOnboardingDetailsRepository.findOneByPriority(validatedPhoneNumberResponse.getData(), crmUserDetailsResponse.getData().get("crmBiometricId")) :
                                    baOnboardingDetailsRepository.findOneByPriority(re_requestDto.getAgentId(), validatedPhoneNumberResponse.getData(), crmUserDetailsResponse.getData().get("crmBiometricId"));

                            if (baOnboardingDetailsOptional.isPresent()) {
                                if (baOnboardingDetailsOptional.get().getStatus().equals(APPLICATION_STATUS.PENDING)
                                        || baOnboardingDetailsOptional.get().getStatus().equals(APPLICATION_STATUS.APPROVED)
                                        || baOnboardingDetailsOptional.get().getStatus().equals(APPLICATION_STATUS.REQUIRED)

                                ) {
                                    logManager.info(loggerCommon.getLog(log, "info",transactionId, 1, "User already exist!", "Phone number [ " + validatedPhoneNumberResponse.getData() + " ] + First name [ " + crmUserDetailsResponse.getData().get("firstName") + " ] "));

                                    LoggingHelper.setTransactionId(transactionId);LoggingHelper.setProcess("BA_CHECK_RESPONSE");LoggingHelper.setOutcome("CONFLICT");
                                    logger.info("business.event=USER_ALREADY_EXISTS status={} initiationType={} transactionId={}", baOnboardingDetailsOptional.get().getStatus(), initiatorType, transactionId);
                                    return ResponseTemplate.error("User already exist!");
                                } else {
                                    baOnboardingDetailsRepository.delete(baOnboardingDetailsOptional.get());
                                    System.err.println("- Deleted -");
                                    LoggingHelper.setTransactionId(transactionId);LoggingHelper.setProcess("BA_CHECK_RESPONSE");LoggingHelper.setOutcome("DELETED");
                                    logger.info("business.event=USER_ALREADY_EXISTS status={} initiationType={} transactionId={} message={}", baOnboardingDetailsOptional.get().getStatus(), initiatorType, transactionId, "User already exist with a status DRAFT");
                                    logManager.info(loggerCommon.getLog(log, "info",transactionId, 1, "User already exist with a status DRAFT!", " Deleted  "));

                                }
                            }
                            Optional<SalesUserDetailNew> sndUserDetail = Optional.empty();

                                if (initiatorType == INITIATION_TYPE.RE_VERIFICATION) {
                                    sndUserDetail = salesUserDetailRepositoryNew.findByUserId(re_requestDto.getAgentId());

                                    if (sndUserDetail.isEmpty()) {
                                    logManager.info(loggerCommon.getLog(log,"info", transactionId, 1, "Agent not found!", "Agent by the specified id [ " + re_requestDto.getAgentId() + " ] is not found!"));
                                    LoggingHelper.setTransactionId(transactionId);LoggingHelper.setProcess("BA_RE_VERIFICATION");LoggingHelper.setOutcome("AGENT_NOT_FOUND");
                                    logger.info("business.event=RE_VERIFICATION_ATTEMPT INITIATION_TYPE={} agentId={} transactionId={}", initiatorType, re_requestDto.getAgentId(), transactionId);
                                    return ResponseTemplate.error("Agent not found!");
                                }

                                String userTypeId = (sndUserDetail.get().getUserType());
                                if (!"Distributor BA".equals(userTypeId)){
                                    logManager.info(loggerCommon.getLog(log, "info",transactionId, 1, "Unauthorized user type!", "Only BA distributors can reverify agents. User type: " + userTypeId));
                                    LoggingHelper.setTransactionId(transactionId);
                                    LoggingHelper.setProcess("BA_RE_VERIFICATION");
                                    LoggingHelper.setOutcome("UNAUTHORIZED_USER_TYPE");
                                    logger.info("business.event=RE_VERIFICATION_ATTEMPT INITIATION_TYPE={} agentId={} transactionId={} userType={} httpStatus={}", initiatorType, re_requestDto.getAgentId(), transactionId, userTypeId, 403);
                                    return ResponseTemplate.error("Only distributor BA can be reverified");
                                }

                            }

                            BAOnboardingDetails baOnboardingDetails = BAOnboardingDetails.builder()
                                    .msisdn(validatedPhoneNumberResponse.getData())
                                    .crmFirstName(crmUserDetailsResponse.getData().get("firstName"))
                                    .crmMiddleName(crmUserDetailsResponse.getData().get("middleName"))
                                    .crmLastName(crmUserDetailsResponse.getData().get("lastName"))
                                    .crmBiometricsId(crmUserDetailsResponse.getData().get("crmBiometricId"))
                                    .status(APPLICATION_STATUS.DRAFT)
                                    .roles(String.join(",", List.of(eKycAgentEKycRole, eKycAgentMPESAEKycRole)))
                                    .createdBy(initiatorEmail)
                                    .createdById(userDetailsResponse.getData().getId())
                                    .build();
                            LoggingHelper.setProcess("BA_DATA");
                            LoggingHelper.setOutcome("SAVED");
                            logger.info("business.event=DATA INITIATION_TYPE={} transactionId={} massage={} ",  initiatorType,
                                     transactionId, "saved common date for both Ba-Onbarding and Re-verified to  OP Table ");


                            // Reverification Flag
                            if (initiatorType == INITIATION_TYPE.RE_VERIFICATION) {

                                baOnboardingDetails.setAgentId(re_requestDto.getAgentId());
                                baOnboardingDetails.setIsReverified(true);
                                baOnboardingDetails.setReverifiedOn(LocalDateTime.now());
                              //  Optional<SalesUserDetailNew> sndUserDetail = salesUserDetailRepositoryNew.findByUserId(re_requestDto.getAgentId());

                                if (sndUserDetail.isPresent()) {
                                    baOnboardingDetails.setSndCreatedBy(String.valueOf(sndUserDetail.get().getCreatedBy()));
                                    baOnboardingDetails.setSndDistributor(sndUserDetail.get().getDistributor());
                                    baOnboardingDetails.setSndDistributorId(sndUserDetail.get().getDistributorId());
                                    LoggingHelper.setTransactionId(transactionId);
                                    LoggingHelper.setProcess("BA_RE_VERIFICATION");
                                    LoggingHelper.setOutcome("SAVED");
                                    logger.info("business.event=RE_VERIFICATION_ATTEMPT INITIATION_TYPE={} transactionId={} massage={} ",  initiatorType,
                                            transactionId, "saved common date for both Ba-Onbarding and Re-verified to  OP Table ");

                                    logManager.info(loggerCommon.getLog(
                                            log, "info", transactionId,0, "Agent reverification in progress", "Agent data migrated from SalesUserDetail to BA onboarding for reverification"
                                    ));
                                } else {
                                    LoggingHelper.setTransactionId(transactionId);
                                    LoggingHelper.setProcess("BA_RE_VERIFICATION");
                                    LoggingHelper.setOutcome("SALES_USER_NOT_FOUND");
                                    logManager.info(loggerCommon.getLog(log, "warn",transactionId ,0, "Unauthorized!", "No SalesUserDetail found for agentId: " ));
                                    logger.info("business.event=BA_RE_VERIFICATION_FAILED INITIATION_TYPE={} agentId={} transactionId={} httpStatus={}",
                                            initiatorType,
                                            re_requestDto.getAgentId(),
                                            transactionId,
                                            404
                                    );
                                    return ResponseTemplate.error("Not Found!");
                                }

                            } else {
                                baOnboardingDetails.setIsReverified(false);

                                LoggingHelper.setTransactionId(transactionId);
                                LoggingHelper.setProcess("BA_ONBOARDING");
                                LoggingHelper.setOutcome("NEW_ONBOARDING");
                                logger.info("business.event=BA_ONBOARDING_INITIATED INITIATION_TYPE={} transactionId={} httpStatus={}",
                                        initiatorType,
                                        transactionId,
                                        200
                                );
                            }


                            ResponseTemplate<BAOnboardingDetails> ekycLastResortResponse = ekycLastResort(baOnboardingDetails, logManager, log , transactionId);
                            if (ekycLastResortResponse.isSuccess()) {
                                baOnboardingDetails = ekycLastResortResponse.getData();
                                baOnboardingDetails = baOnboardingDetailsRepository.save(baOnboardingDetails);
                                Map<String, Object> response = new LinkedHashMap<>();
                                Map<String, Object> responseData = new LinkedHashMap<>();
                                response.put("id", baOnboardingDetails.getId());
                                response.put("sessionId", baOnboardingDetails.getSessionId());
                                Optional<BA_ONBOARDING_APP_PAGES> pageOptional = BA_ONBOARDING_APP_PAGES.getPageByField("msisdn");
                                response.put("pageId", pageOptional.map(Enum::name).orElse("N/A"));
                                responseData.put("crmFirstName", baOnboardingDetails.getCrmFirstName());
                                responseData.put("crmMiddleName", baOnboardingDetails.getCrmMiddleName());
                                responseData.put("crmLastName", baOnboardingDetails.getCrmLastName());
                                responseData.put("msisdn", baOnboardingDetails.getMsisdn());
                                response.put("data", responseData);
                                LoggingHelper.setOutcome("SUCCESS");
                                logger.info("business.event=INITIATE_BA_REQUEST_SUCCESS INITIATION_TYPE={} msisdn={} agentId={} transactionId={} httpStatus={}",
                                        initiatorType,
                                        requestDto != null ? requestDto.getPhoneNumber() : re_requestDto.getPhoneNumber(),
                                        initiatorType == INITIATION_TYPE.RE_VERIFICATION ? re_requestDto.getAgentId() : "N/A",
                                        transactionId,
                                        200);

                                return ResponseTemplate.success(response);
                            } else {
                                return ekycLastResortResponse;
                            }
                        } else {
                            return crmUserDetailsResponse;
                        }
                    } else {
                        return validatedPhoneNumberResponse;
                    }
                }
                else {
                    logManager.info(loggerCommon.getLog(log,"info",transactionId, 0, "Unauthorized!", "User by the specified username [ " + initiatorEmail + " ] with the roles [ " + userDetailsResponse.getData().getUserHierarchies() + " ] is not authorized!"));
                    return ResponseTemplate.error("Unauthorized!");
                }
            } else {
                return userDetailsResponse;
            }
        } catch (Exception e) {
            e.printStackTrace();
            logManager.error(loggerCommon.getLog(log,transactionId, 1, "Something went wrong! Please try again.", e));

            LoggingHelper.setOutcome("ERROR");
            logger.info("business.event=INITIATE_BA_ERROR INITIATION_TYPE={} msisdn={} agentId={} transactionId={} httpStatus={}", initiatorType, requestDto != null ? requestDto.getPhoneNumber() : "N/A", re_requestDto != null ? re_requestDto.getAgentId() : "N/A", transactionId, 500);
            return ResponseTemplate.error("Something went wrong! Please try again.");
        }
    }

    public ResponseTemplate<?> baOnboardingUpdateRouter(BAOnboardingUpdateRequestDto requestDto, String userAgent, OPTIONAL_PARAM options, LogManager logManager) {
        System.out.println("User Update -- User-Agent: " + userAgent);
        String source = Helper.identifyRequestSource(userAgent);
        System.out.println("User Update -- Source: " + source);

        try {
            ObjectMapper objectMapper = new ObjectMapper();
            objectMapper.registerModule(new JavaTimeModule());
            System.err.println("Update Request: " + objectMapper.writerWithDefaultPrettyPrinter().writeValueAsString(requestDto));
        }
        catch (Exception e) {
            System.err.println("Parsing Exception: " + e.getMessage());
        }

        switch (source) {
            case "MOBILE_APP" -> {
                return continueBAOnboarding(requestDto, logManager);
            }
            case "WEB" -> {
                if (options != null) {
                    switch (options) {
                        case ADJUDICATE -> {
                            return performAdjudication(requestDto, logManager );
                        }
                        case EDIT -> {
                            return performEdit(requestDto, logManager);
                        }
                        default -> {
                            return ResponseTemplate.error("Unsupported Request!");
                        }
                    }
                } else {
                    return ResponseTemplate.error("Bad Request! Purpose must be specified!");
                }
            }
            default -> {
                return ResponseTemplate.error("Bad Request!");
            }
        }
    }

    private ResponseTemplate<?> continueBAOnboarding(BAOnboardingUpdateRequestDto requestDto, LogManager logManager) {
        String transactionId = "ONE_PLATFORM_BA_ONBOARDING_" +  UUID.randomUUID().toString() + System.currentTimeMillis();
        Log log = new Log(transactionId, "info", "Continue BA Onboarding", "", "", "", "ONE_PLATFORM_BA_ONBOARDING", "ONE_PLATFORM_BA_ONBOARDING",
                "0", "Default!", "Default Detailed Message.", "");

        try {
            LoggingHelper.setTransactionId(transactionId);
            LoggingHelper.setProcess("CONTINUE_BA_ONBOARDING");
            String initiatorEmail = helper.extractEmailFromRequest(logManager, log , transactionId);

            ResponseTemplate<UserDetailsResponseDto> userDetailsResponse = commonService.getUserBasicDetails(null, initiatorEmail, internalUsersEntityRepository, externalUsersEntityRepository, logManager, log);
            if (userDetailsResponse.isSuccess()) {
                LoggingHelper.setOutcome("UNAUTHORIZED");
                        logger.info("business.event=CONTINUE_BA_ONBOARDING_FAILED initiatorEmail={} transactionId={} httpStatus={}",
                        initiatorEmail, transactionId, 403);
                if (Helper.hasAccess(Authorization.CAN_ONBOARD_APP, userDetailsResponse.getData().getUserHierarchies())) {
                    if (requestDto.getSessionId() != null) {
                        Optional<BAOnboardingDetails> existingBAOnboardingDetailsOptional = baOnboardingDetailsRepository.findBySessionId(requestDto.getSessionId());
                        if (existingBAOnboardingDetailsOptional.isPresent()) {
                            if (existingBAOnboardingDetailsOptional.get().getCreatedBy().equals((initiatorEmail)) || existingBAOnboardingDetailsOptional.get().getCreatedById().equals(userDetailsResponse.getData().getId())) {
                                List<String> excludedFields = List.of("id", "sessionId", "faydaNumber", "nidIDExpirationDate", "route", "isDone");
                                List<String> nonEmptyFields = Helper.extractNonEmptyFields(requestDto, excludedFields);
                                logManager.info(loggerCommon.getLog(log, "info",transactionId, 0, "", nonEmptyFields));

                                if (nonEmptyFields.isEmpty()) {
                                    logManager.info(loggerCommon.getLog(log, "info", transactionId,1, "Mandatory fields are missing!", nonEmptyFields));
                                    return ResponseTemplate.error("Mandatory fields are missing!");
                                }

                                Optional<BA_ONBOARDING_APP_PAGES> pageOptional = BA_ONBOARDING_APP_PAGES.getPageByField(nonEmptyFields.get(0));
                                if (pageOptional.isEmpty()) {
                                    return ResponseTemplate.error("Unsupported detail!");
                                }

                                switch (pageOptional.get()) {
                                    case BIOMETRICS_PAGE -> { // Note: eKYC Validations
                                        return biometricsPageDataHandler( requestDto, existingBAOnboardingDetailsOptional.get(), initiatorEmail, logManager, log);
                                    }
                                    case CAPTURE_PHOTO_PAGE -> { // Note: Persisting Agent Photo Path
                                        return capturePhotoPageDataHandler(requestDto, existingBAOnboardingDetailsOptional.get(), initiatorEmail, logManager, log);
                                    }
                                    case NID_PICTURE_PAGE -> { // Note: Persisting NID Card Pictures Path
                                        return nidPicturePageDataHandler(requestDto, existingBAOnboardingDetailsOptional.get(), initiatorEmail, logManager, log);
                                    }
                                    case PERSONAL_INFORMATION_PAGE -> { // Note: Persisting NID Profile Details and Letter of Consent
                                        return nidProfilePageDataHandler(requestDto, existingBAOnboardingDetailsOptional.get(), initiatorEmail, logManager, log);
                                    }
                                    case DISTRIBUTION_INFORMATION_PAGE -> {
                                        return distributionInformationPageDataHandler(requestDto, existingBAOnboardingDetailsOptional.get(), initiatorEmail, userDetailsResponse.getData(), logManager, log);
                                    }
                                    default -> {
                                        logManager.info(loggerCommon.getLog(log, "info", transactionId,1, "Unknown request!", pageOptional.get()));
                                        return ResponseTemplate.error("Unknown request!");
                                    }
                                }
                            } else {
                                logManager.info(loggerCommon.getLog(log, "info", transactionId,1, "Unauthorized!", "User by the specified username [ " + initiatorEmail + " ] is trying to continue another user's [ " + existingBAOnboardingDetailsOptional.get().getCreatedBy() + " - LT: " + existingBAOnboardingDetailsOptional.get().getCreatedById() + " ] ba onboarding process!"));
                                return ResponseTemplate.error("Unauthorized!");
                            }
                        }
                        else {
                            logManager.info(loggerCommon.getLog(log, "info", transactionId,1, "Session not found!", "Specified Session ID [ " + requestDto.getSessionId() + " ] - Initiator [ " + initiatorEmail + " ] "));
                            return ResponseTemplate.error("Session not found!");
                        }
                    }
                    else {
                        logManager.info(loggerCommon.getLog(log, "info", transactionId,1, "Bad Request! Session must be specified!", "Initiator [ " + initiatorEmail + " ] "));
                        return ResponseTemplate.error("Bad Request! Session must be specified!");
                    }
                }
                else {
                    logManager.info(loggerCommon.getLog(log, "info",transactionId, 1, "Unauthorized!", "User by the specified username [ " + initiatorEmail + " ] with the roles [ " + userDetailsResponse.getData().getUserHierarchies() + " ] is not authorized!"));
                    return ResponseTemplate.error("Unauthorized!");
                }
            } else {
                return userDetailsResponse;
            }
        } catch (Exception e) {
            e.printStackTrace();
            logManager.error(loggerCommon.getLog(log, transactionId, 1,"Something went wrong! Please try again.", e));
            return ResponseTemplate.error("Something went wrong! Please try again.");
        }
    }

    private ResponseTemplate<?> biometricsPageDataHandler(BAOnboardingUpdateRequestDto requestDto, BAOnboardingDetails updateBAOnboardingDetails, String initiatorEmail, LogManager logManager, Log log) {
        LoggingHelper.setProcess("BIOMETRICS_PAGE");
        log.setProcess("Handling Biometrics Page");
        if (requestDto.getBiometricsList() == null || requestDto.getBiometricsList().isEmpty()) {
            LoggingHelper.setOutcome("MISSING_BIOMETRICS_DATA");
             logger.info("business.event=BIOMETRICS_PAGE_FAILED initiatorEmail={} httpStatus={}", initiatorEmail, 400);
            return ResponseTemplate.error("Missing biometrics data!");
        }

        ResponseEntity<?> biometricsBase64Response = fileService.getFile(requestDto.getBiometricsList().get(0).getBiometricsFilePath(), REQUIRED_FILE_TYPE.BASE64, logManager);
        if (biometricsBase64Response.getStatusCode().is2xxSuccessful()) {
            EKycVerificationApiRequestDto eKycVerificationApiRequestDto = EKycVerificationApiRequestDto.builder()
                    .biometricData(List.of(
                            EKycVerificationApiRequestDto.BiometricData.builder()
                                    .biometricType(requestDto.getBiometricsList().get(0).getBiometricsType().name())
                                    .biometricSubType(requestDto.getBiometricsList().get(0).getBiometricsSubType())
                                    .instance("ONE_PLATFORM_CTAPP")
                                    .image(biometricsBase64Response.getBody().toString())
                                    .captureDate(LocalDate.now().toString())
                                    .captureDevice("CTAPP")
                                    .impressionType("LIVE_SCAN_PLAIN")
                                    .compression("WSQ")
                                    .metadata("string")
                                    .comment("BA_ONBOARDING_ON_ONE_PLATFORM_CTAPP")
                                    .build()
                    ))
                    .build();

            ResponseTemplate<?> eKycVerificationResponse = apiGatewayService.eKycVerification(eKycVerificationApiRequestDto, updateBAOnboardingDetails.getCrmBiometricsId(), logManager, log);
            if (eKycVerificationResponse.isSuccess()) {
                ResponseTemplate<List<String>> eKycDeDupHandlerResponse = apiGatewayService.eKycDeDupHandler(eKycVerificationApiRequestDto, updateBAOnboardingDetails.getCrmBiometricsId(), EKYC_DEDUB_TYPE.BY_BIOMETRICS, logManager, log);
                if (eKycDeDupHandlerResponse.isSuccess()) {
                    List<BAOnboardingDetails> findAllByBiometricsIdList = baOnboardingDetailsRepository.findAllByBiometricsIdList(eKycDeDupHandlerResponse.getData());
                    if (findAllByBiometricsIdList.isEmpty() || findAllByBiometricsIdList.stream().filter(ba -> !ba.getStatus().equals(APPLICATION_STATUS.DRAFT)).toList().isEmpty()) {
                        ResponseTemplate<?> eKycVettingResponse = apiGatewayService.eKycVetting(updateBAOnboardingDetails.getCrmBiometricsId(), logManager, log);
                        if (eKycVettingResponse.isSuccess()) {
                            if (eKycDeDupHandlerResponse.getData() != null) {
                                updateBAOnboardingDetails.getBiometricsIdList().addAll(eKycDeDupHandlerResponse.getData());
                            }

                            updateBAOnboardingDetails.getBiometricsList().add(
                                    BiometricsData.builder()
                                            .biometricsFilePath(requestDto.getBiometricsList().get(0).getBiometricsFilePath())
                                            .biometricsType(requestDto.getBiometricsList().get(0).getBiometricsType())
                                            .biometricsSubType(requestDto.getBiometricsList().get(0).getBiometricsSubType())
                                            .build()
                            );

                            updateBAOnboardingDetails.setUpdatedBy(initiatorEmail);
                            updateBAOnboardingDetails.setStatus(getOnboardingDetailStatus(updateBAOnboardingDetails, requestDto));

                            updateBAOnboardingDetails = baOnboardingDetailsRepository.save(updateBAOnboardingDetails);

                            Map<String, Object> response = new LinkedHashMap<>();
                            response.put("id", updateBAOnboardingDetails.getId());
                            response.put("sessionId", updateBAOnboardingDetails.getSessionId());
                            response.put("pageId", BA_ONBOARDING_APP_PAGES.BIOMETRICS_PAGE);
                            response.put("data", new LinkedHashMap<>());

                            return ResponseTemplate.success(response);
                        }
                        else {
                            logManager.info(loggerCommon.getLog(log, "info", log.getTransactionID(), 1, "Verification failed!", "Vetting failed for this id [ " + updateBAOnboardingDetails.getCrmBiometricsId() + " ]"));
                            return ResponseTemplate.error("Verification failed!");
                        }
                    }
                    else {
                        logManager.info(loggerCommon.getLog(log, "info", log.getTransactionID(), 1, "User already exists!", "Duplicated biometrics data found!"));
                        return ResponseTemplate.error("User already exists!");
                    }
                } else {
                    return eKycDeDupHandlerResponse;
                }
            } else {
                return eKycVerificationResponse;
            }
        }
        else {
            logManager.info(loggerCommon.getLog(log, "info", log.getTransactionID(), 1, "Something went wrong! Please try again!", biometricsBase64Response));
            return ResponseTemplate.error("Something went wrong! Please try again!");
        }
    }

    private ResponseTemplate<?> capturePhotoPageDataHandler(BAOnboardingUpdateRequestDto requestDto, BAOnboardingDetails updateBAOnboardingDetails, String initiatorEmail, LogManager logManager, Log log) {
        log.setProcess("Handling Photo Capture Page");
        if (requestDto.getAgentPhotoList() == null || requestDto.getAgentPhotoList().isEmpty()) {
            return ResponseTemplate.error("Missing agent photo!");
        }

        ResponseEntity<?> biometricsBase64Response = fileService.getFile(requestDto.getAgentPhotoList().get(0), REQUIRED_FILE_TYPE.BASE64, logManager);
        if (biometricsBase64Response.getStatusCode().is2xxSuccessful()) {
            updateBAOnboardingDetails.getAgentPhotoList().add(requestDto.getAgentPhotoList().get(0));
            updateBAOnboardingDetails.setUpdatedBy(initiatorEmail);
            updateBAOnboardingDetails.setStatus(getOnboardingDetailStatus(updateBAOnboardingDetails, requestDto));

            updateBAOnboardingDetails = baOnboardingDetailsRepository.save(updateBAOnboardingDetails);

            Map<String, Object> response = new LinkedHashMap<>();
            response.put("id", updateBAOnboardingDetails.getId());
            response.put("sessionId", updateBAOnboardingDetails.getSessionId());
            response.put("pageId", BA_ONBOARDING_APP_PAGES.CAPTURE_PHOTO_PAGE);
            response.put("data", new LinkedHashMap<>());

            return ResponseTemplate.success(response);
        }
        else {
            logManager.info(loggerCommon.getLog(log, "info", log.getTransactionID(), 1, "Agent photo not found!", biometricsBase64Response));
            return ResponseTemplate.error("Agent photo not found!");
        }
    }

    private ResponseTemplate<?> nidPicturePageDataHandler(BAOnboardingUpdateRequestDto requestDto, BAOnboardingDetails updateBAOnboardingDetails, String initiatorEmail, LogManager logManager, Log log) {
        log.setProcess("Handling NID Picture Page");
        if (requestDto.getNidCardPictureList() == null || requestDto.getNidCardPictureList().isEmpty()) {
            return ResponseTemplate.error("Missing NID Cards!");
        }

        ResponseTemplate<?> validateNIDCardPicturesResponse = helper.validateNIDCardPictures(requestDto, fileService, logManager, log);
        if (validateNIDCardPicturesResponse.isSuccess()) {
            List<NIDCardPicture> updatedNidCardPictureList = updateBAOnboardingDetails.getNidCardPictureList() != null ? new ArrayList<>(updateBAOnboardingDetails.getNidCardPictureList()) : new ArrayList<>();

            requestDto.getNidCardPictureList().forEach(cardPicture ->
                    updatedNidCardPictureList.add(
                            NIDCardPicture.builder()
                                    .nidPicturePath(cardPicture.getNidCardPicturePath())
                                    .nidPictureType(cardPicture.getNidCardPictureType())
                                    .build()
                    )
            );

            updateBAOnboardingDetails.setNidCardPictureList(updatedNidCardPictureList);
            updateBAOnboardingDetails.setUpdatedBy(initiatorEmail);
            updateBAOnboardingDetails.setStatus(getOnboardingDetailStatus(updateBAOnboardingDetails, requestDto));
            updateBAOnboardingDetails = baOnboardingDetailsRepository.save(updateBAOnboardingDetails);

            Map<String, Object> response = new LinkedHashMap<>();
            response.put("id", updateBAOnboardingDetails.getId());
            response.put("sessionId", updateBAOnboardingDetails.getSessionId());
            response.put("pageId", BA_ONBOARDING_APP_PAGES.NID_PICTURE_PAGE);
            response.put("data", new LinkedHashMap<>());

            return ResponseTemplate.success(response);
        }
        else {
            logManager.info(loggerCommon.getLog(log, "info", log.getTransactionID(), 1, "Invalid NID card picture!", validateNIDCardPicturesResponse));
            return validateNIDCardPicturesResponse;
        }
    }

    private ResponseTemplate<?> nidProfilePageDataHandler(BAOnboardingUpdateRequestDto requestDto, BAOnboardingDetails updateBAOnboardingDetails, String initiatorEmail, LogManager logManager, Log log) {
        log.setProcess("Handling NID Profile Page");
        ResponseEntity<?> biometricsBase64Response = fileService.getFile(requestDto.getNidPhotoPath(), REQUIRED_FILE_TYPE.BASE64, logManager);
        if (biometricsBase64Response.getStatusCode().is2xxSuccessful()) {
            ResponseEntity<?> biometricsBase64ForLetterOfConsentResponse = fileService.getFile(requestDto.getLetterOfConsentList().get(0), REQUIRED_FILE_TYPE.BASE64, logManager);
            if (!biometricsBase64ForLetterOfConsentResponse.getStatusCode().is2xxSuccessful()) {
                logManager.info(loggerCommon.getLog(log, "info", log.getTransactionID(), 1, "Letter of Consent not found!", biometricsBase64ForLetterOfConsentResponse));
                return ResponseTemplate.error("Letter of Consent not found!");
            }

            Optional<BAOnboardingDetails> existingBaOnboardingDetailsOptional = baOnboardingDetailsRepository.findByNidSubjectId(requestDto.getNidSubjectId());
            if (
                    existingBaOnboardingDetailsOptional.isPresent() && (updateBAOnboardingDetails.getStatus().equals(APPLICATION_STATUS.PENDING) || updateBAOnboardingDetails.getStatus().equals(APPLICATION_STATUS.APPROVED))
            ) {
                logManager.error(loggerCommon.getLog(log, "error", log.getTransactionID(), 1, "An agent already exist!", existingBaOnboardingDetailsOptional.get().getNidSubjectId()));
                return ResponseTemplate.error("An agent already exist!");
            }

            if (existingBaOnboardingDetailsOptional.isEmpty()) {
                Map<String, String> names = Helper.parseFullName(requestDto.getNidFullName());
                updateBAOnboardingDetails.setNidFirstName(names.get("firstName"));
                updateBAOnboardingDetails.setNidMiddleName(names.get("middleName"));
                updateBAOnboardingDetails.setNidLastName(names.get("lastName"));

                updateBAOnboardingDetails.setNidSubjectId(requestDto.getNidSubjectId());

                updateBAOnboardingDetails.setNidDateOfBirth(requestDto.getNidDateOfBirth());
                updateBAOnboardingDetails.setNidIDExpirationDate(requestDto.getNidIDExpirationDate());
                updateBAOnboardingDetails.setNidAddress(
                        Address.builder()
                                .zone(requestDto.getNidAddress().getZone())
                                .kebele(requestDto.getNidAddress().getKebele())
                                .woreda(requestDto.getNidAddress().getWoreda())
                                .nidRegion(requestDto.getNidAddress().getRegion())
                                .build()
                );

                updateBAOnboardingDetails.setNidPhotoPath(requestDto.getNidPhotoPath());
            }

            String mergedLetterOfConsentPath = "";
            if (requestDto.getLetterOfConsentList().size() == 1) {
                mergedLetterOfConsentPath = requestDto.getLetterOfConsentList().get(0);
            }
            else {
                mergedLetterOfConsentPath = getLetterOfConsentAsPdfFilePath(requestDto.getLetterOfConsentList(), updateBAOnboardingDetails.getId(), FILE_GROUP.LETTER_OF_CONSENT, logManager, log);
            }

            if (mergedLetterOfConsentPath == null || mergedLetterOfConsentPath.isBlank()) {
                return ResponseTemplate.error("Unable to identify the letter of consent! Please try again.");
            }

            updateBAOnboardingDetails.getLetterOfConsentList().add(mergedLetterOfConsentPath);

            updateBAOnboardingDetails.setUpdatedBy(initiatorEmail);
            updateBAOnboardingDetails.setStatus(getOnboardingDetailStatus(updateBAOnboardingDetails, requestDto));

            updateBAOnboardingDetails = baOnboardingDetailsRepository.save(updateBAOnboardingDetails);

            Map<String, Object> response = new LinkedHashMap<>();
            response.put("id", updateBAOnboardingDetails.getId());
            response.put("sessionId", updateBAOnboardingDetails.getSessionId());
            response.put("pageId", BA_ONBOARDING_APP_PAGES.PERSONAL_INFORMATION_PAGE);
            response.put("data", new LinkedHashMap<>());

            return ResponseTemplate.success(response);
        }
        else {
            logManager.info(loggerCommon.getLog(log, "info", log.getTransactionID(), 1, "Agent NID photo not found!", biometricsBase64Response));
            return ResponseTemplate.error("Agent NID photo not found!");
        }
    }

    private String getLetterOfConsentAsPdfFilePath(List<String> imagePaths, Long id, FILE_GROUP fileGroup, LogManager logManager, Log log) {
        try {
            List<BufferedImage> bufferedImages = new ArrayList<>();

            for (String path : imagePaths) {
                ResponseEntity<?> response = fileService.getFile(path, REQUIRED_FILE_TYPE.BASE64, logManager);

                if (response.getStatusCode() != HttpStatus.OK || !(response.getBody() instanceof String)) {
                    throw new RuntimeException("Error loading image: " + path);
                }

                byte[] decodedBytes = Base64.getDecoder().decode((String) response.getBody());
                ByteArrayInputStream bis = new ByteArrayInputStream(decodedBytes);
                bufferedImages.add(ImageIO.read(bis));
            }

            // :Merging into PDF
            ByteArrayOutputStream mergedPdfOutput = new ByteArrayOutputStream();
            try (PDDocument doc = new PDDocument()) {
                for (BufferedImage img : bufferedImages) {
                    PDPage page = new PDPage(new PDRectangle(img.getWidth(), img.getHeight()));
                    doc.addPage(page);
                    PDImageXObject pdImage = LosslessFactory.createFromImage(doc, img);
                    try (PDPageContentStream contentStream = new PDPageContentStream(doc, page)) {
                        contentStream.drawImage(pdImage, 0, 0, img.getWidth(), img.getHeight());
                    }
                }
                doc.save(mergedPdfOutput);
            }

            // :Encode merged PDF to base64
            String base64Pdf = Base64.getEncoder().encodeToString(mergedPdfOutput.toByteArray());
            String base64PdfFormatted = "data:application/pdf;base64," + base64Pdf;

            // :Save the PDF file
            UploadFileByBase46RequestDto saveRequest = UploadFileByBase46RequestDto.builder().fileGroup(fileGroup).id(id).fileBase64(base64PdfFormatted).build();
            ResponseTemplate<String> responseTemplate = fileService.saveFileFromBase64(saveRequest, logManager);
            if (!responseTemplate.isSuccess()) {
                throw new RuntimeException("Failed to save merged PDF.");
            }

            String pdfPath = responseTemplate.getData();

            if (pdfPath != null && !pdfPath.isBlank()) {
                fileService.deleteFilesByPaths(imagePaths, logManager, log);
                return pdfPath;
            } else {
                return null;
            }
        } catch (Exception e) {
            e.printStackTrace();
            logManager.error(loggerCommon.getLog(log, "error", 1, "Something went wrong! Please try again.", e));
            return null;
        }
    }

    private ResponseTemplate<?> distributionInformationPageDataHandler(BAOnboardingUpdateRequestDto requestDto, BAOnboardingDetails updateBAOnboardingDetails, String initiatorEmail, UserDetailsResponseDto onboarderDetail, LogManager logManager, Log log) {
        log.setProcess("Handling Distribution Information Page");
        Optional<InternalUsersEntity> rsmUserEntityOptional = internalUsersEntityRepository.findByUserName(requestDto.getRsmUsername());
        if (rsmUserEntityOptional.isPresent()) {
            if (!rsmUserEntityOptional.get().getIsActive() && !rsmUserEntityOptional.get().getStatus().equals(UM_USER_STATUS.ACTIVE)) {
                logManager.info(loggerCommon.getLog(log, "info", log.getTransactionID(), 1, "Unauthorized!", "RSM user by the specified username [ " + requestDto.getRsmUsername() + " ] is not active."));
                return ResponseTemplate.error("RSM user is not active!");
            }
            Optional<InternalUsersEntity> ramUserEntityOptional = internalUsersEntityRepository.findByUserName(requestDto.getRamUsername());
            if (ramUserEntityOptional.isEmpty()) {
                if (!rsmUserEntityOptional.get().getIsActive() && !rsmUserEntityOptional.get().getStatus().equals(UM_USER_STATUS.ACTIVE)) {
                    logManager.info(loggerCommon.getLog(log, "info", log.getTransactionID(), 1, "Unauthorized!", "RAM user by the specified username [ " + requestDto.getRsmUsername() + " ] is not active."));
                    return ResponseTemplate.error("RAM user is not active!");
                }
                logManager.info(loggerCommon.getLog(log, "info", log.getTransactionID(), 1, "The specified RAM is not available.", "ID: " + requestDto.getRamUsername()));
                return ResponseTemplate.error("The specified RAM is not available.");
            }

            updateBAOnboardingDetails.setRsm(requestDto.getRsmUsername());
            updateBAOnboardingDetails.setRam(requestDto.getRamUsername());

            Optional<ChannelsEntity> distributorChannelsEntityOptional = channelsEntityRepository.findById(requestDto.getDistributorId());
            if (distributorChannelsEntityOptional.isEmpty()) {
                logManager.info(loggerCommon.getLog(log, "info", log.getTransactionID(), 1, "The specified Distributor is not available.", "ID: " + requestDto.getDistributorId()));
                return ResponseTemplate.error("The specified Distributor is not available.");
            }

            Optional<ChannelsEntity> distributorShopChannelsEntityOptional = channelsEntityRepository.findById(requestDto.getDistributorId());
            if (distributorShopChannelsEntityOptional.isEmpty()) {
                logManager.info(loggerCommon.getLog(log, "info", log.getTransactionID(), 1, "The specified Distributor Shop is not available.", "ID: " + requestDto.getDistributorShopId()));
                return ResponseTemplate.error("The specified Distributor Shop is not available.");
            }

            updateBAOnboardingDetails.setDistributorId(requestDto.getDistributorId());
            updateBAOnboardingDetails.setDistributorShopId(requestDto.getDistributorShopId());

            updateBAOnboardingDetails.setLatitude(requestDto.getLatitude());
            updateBAOnboardingDetails.setLongitude(requestDto.getLongitude());

            updateBAOnboardingDetails.setDivision(requestDto.getDivision());
            updateBAOnboardingDetails.setRegion(requestDto.getRegion());
            updateBAOnboardingDetails.setCluster(requestDto.getCluster());
            updateBAOnboardingDetails.setRoute(requestDto.getRoute());
            updateBAOnboardingDetails.setSite(requestDto.getSite());

            updateBAOnboardingDetails.setUpdatedBy(initiatorEmail);
            updateBAOnboardingDetails.setStatus(getOnboardingDetailStatus(updateBAOnboardingDetails, requestDto));
            updateBAOnboardingDetails.setReasonForApplicationStatusUpdate(requestDto.getReasonForApplicationStatusUpdate());


            updateBAOnboardingDetails = baOnboardingDetailsRepository.save(updateBAOnboardingDetails);

            Map<String, Object> response = new LinkedHashMap<>();
            response.put("id", updateBAOnboardingDetails.getId());
            response.put("sessionId", updateBAOnboardingDetails.getSessionId());
            response.put("pageId", BA_ONBOARDING_APP_PAGES.DISTRIBUTION_INFORMATION_PAGE);
            response.put("data", new LinkedHashMap<>());

            String onboarderFullName = Helper.formatFullName(onboarderDetail.getFirstName(), onboarderDetail.getMiddleName(), onboarderDetail.getLastName());

//          String content = MessageFormat.format(adjudicatorNotificationSMSContent, rsmUserEntityOptional.get().getFirstName(), updateBAOnboardingDetails.getId(), onboarderFullName);
//
//          diff sms for reverification and new onboarding
            String notificationTemplate = updateBAOnboardingDetails.getIsReverified() ? adjudicatorNotificationReverifiedSMSContent : adjudicatorNotificationSMSContent;
            String content = MessageFormat.format(notificationTemplate, rsmUserEntityOptional.get().getFirstName(), updateBAOnboardingDetails.getId(), onboarderFullName);

            SmsRequestDto smsRequestDto = SmsRequestDto.builder()
                    .RequestRefID(log.getTransactionID())
                    .Receiver(List.of(et.safaricom.agent_onboarding.dto.request.api.SmsRequestDto.Receiver.builder().id(rsmUserEntityOptional.get().getContactPhone()).build()))
                    .Content(content)
                    .build();

            apiGatewayService.sendSms(smsRequestDto);
            logManager.info(loggerCommon.getLog(log, "info", log.getTransactionID(), 0, "SMS Sent for RSM!", "RSM: " + rsmUserEntityOptional.get().getUserName()));

            content = MessageFormat.format(adjudicatorNotificationSMSContent, ramUserEntityOptional.get().getFirstName(), updateBAOnboardingDetails.getId(), onboarderFullName);
            smsRequestDto = SmsRequestDto.builder()
                    .RequestRefID(log.getTransactionID())
                    .Receiver(List.of(et.safaricom.agent_onboarding.dto.request.api.SmsRequestDto.Receiver.builder().id(ramUserEntityOptional.get().getContactPhone()).build()))
                    .Content(content)
                    .build();

            apiGatewayService.sendSms(smsRequestDto);
            logManager.info(loggerCommon.getLog(log, "info", log.getTransactionID(), 0, "SMS Sent for RAM!", "RAM: " + ramUserEntityOptional.get().getUserName()));

            logManager.info(loggerCommon.getLog(log, "info", log.getTransactionID(), 0, "Success!", "Onboarding Last Response: " + response));
            return ResponseTemplate.success(response);
        }
        else {
            logManager.info(loggerCommon.getLog(log, "info", log.getTransactionID(), 1, "The specified RSM is not available.", "ID: " + requestDto.getRsmUsername()));
            return ResponseTemplate.error("The specified RSM is not available.");
        }
    }

    private APPLICATION_STATUS getOnboardingDetailStatus(BAOnboardingDetails baOnboardingDetails, BAOnboardingUpdateRequestDto requestDto) {
        if (Boolean.TRUE.equals(requestDto.getIsDone())) {
            return APPLICATION_STATUS.PENDING;
        }
        return baOnboardingDetails.getStatus();
    }

    private ResponseTemplate<?> performAdjudication(BAOnboardingUpdateRequestDto requestDto, LogManager logManager ) {
         String [] unqiue=UUID.randomUUID().toString().split("-");
      String transactionId = "ONE_PLATFORM_BA_ONBOARDING_" +  unqiue[unqiue.length-1]+ System.currentTimeMillis();

        Log log = new Log(transactionId, "info", "Performing an Adjudication", "", "", "", "ONE_PLATFORM_BA_ONBOARDING", "ONE_PLATFORM_BA_ONBOARDING",
                "0", "Default!", "Default Detailed Message.", "");

        try {
            String initiatorEmail = helper.extractEmailFromRequest(logManager, log, transactionId);
            ResponseTemplate<UserDetailsResponseDto> userDetailsResponse = commonService.getUserBasicDetails(null, initiatorEmail, internalUsersEntityRepository, externalUsersEntityRepository, logManager, log);
            if (userDetailsResponse.isSuccess()) {
                if (Helper.hasAccess(Authorization.CAN_ADJUDICATE_WEB, userDetailsResponse.getData().getUserHierarchies())) {
                    if (requestDto.getId() != null && requestDto.getId() > 0L) {
                        Optional<BAOnboardingDetails> existingBAOnboardingDetailsOptional = baOnboardingDetailsRepository.findById(requestDto.getId());
                        if (existingBAOnboardingDetailsOptional.isPresent()) {
                            BAOnboardingDetails updateBAOnboardingDetails = existingBAOnboardingDetailsOptional.get();
                            if (updateBAOnboardingDetails.getRsm().equals(initiatorEmail) || updateBAOnboardingDetails.getRam().equals(initiatorEmail)) {
                                if (requestDto.getApplicationStatus() != null) {
                                    if (requestDto.getApplicationStatus().equals(APPLICATION_STATUS.DRAFT) || requestDto.getApplicationStatus().equals(APPLICATION_STATUS.PENDING)) {
                                        return ResponseTemplate.error("Bad Request!");
                                    }
                                    if (requestDto.getApplicationStatus().equals(APPLICATION_STATUS.REQUIRED) && requestDto.getUpdatableFields().isEmpty()) {
                                        return ResponseTemplate.error("Please specify the fields required to be updated.");
                                    } else {
                                        if (!requestDto.getApplicationStatus().equals(APPLICATION_STATUS.APPROVED)) {

                                            updateBAOnboardingDetails.setReasonForApplicationStatusUpdate(requestDto.getReasonForApplicationStatusUpdate());
                                            updateBAOnboardingDetails.setStatus(requestDto.getApplicationStatus());

                                            if (requestDto.getRemark() != null && !requestDto.getRemark().isBlank()) {
                                               updateBAOnboardingDetails.getBoRemarkList().add(requestDto.getRemark());
                                               updateBAOnboardingDetails.setReasonForApplicationStatusUpdate(requestDto.getReasonForApplicationStatusUpdate());

                                            }

                                            if (requestDto.getApplicationStatus().equals(APPLICATION_STATUS.REJECTED)) {
                                                updateBAOnboardingDetails.setMsisdn("rejected-" + updateBAOnboardingDetails.getMsisdn() + "-" + System.currentTimeMillis());
                                                updateBAOnboardingDetails.setCrmBiometricsId("rejected-" + updateBAOnboardingDetails.getCrmBiometricsId() + "-" + System.currentTimeMillis());
                                                ArrayList<String> existingBiometricIds = new ArrayList<>(updateBAOnboardingDetails.getBiometricsIdList());
                                                String currentTimeStamp = String.valueOf(System.currentTimeMillis());
                                                existingBiometricIds.replaceAll(id -> "rejected-" + id + "-" + currentTimeStamp);
                                                updateBAOnboardingDetails.setBiometricsIdList(existingBiometricIds);
                                                updateBAOnboardingDetails.setNidSubjectId("rejected-" + updateBAOnboardingDetails.getNidSubjectId() + "-" + System.currentTimeMillis());
                                                updateBAOnboardingDetails.setReasonForApplicationStatusUpdate("rejected---" + updateBAOnboardingDetails.getReasonForApplicationStatusUpdate());
                                            }
                                            if (requestDto.getUpdatableFields() != null && !requestDto.getUpdatableFields().isEmpty()) {
                                                String updatableField = String.join(",", requestDto.getUpdatableFields());
                                                updateBAOnboardingDetails.getBoUpdatableFieldList().add(BOUpdatableFields.builder().updatableFields(updatableField).requestedBy(initiatorEmail).requestedOn(LocalDateTime.now()).build());
                                            }
                                            updateBAOnboardingDetails.setUpdatedBy(initiatorEmail);

                                            updateBAOnboardingDetails = baOnboardingDetailsRepository.save(updateBAOnboardingDetails);

                                            ResponseTemplate<UserDetailsResponseDto> onboarderDetailsResponse = commonService.getUserBasicDetails(updateBAOnboardingDetails.getCreatedById(), updateBAOnboardingDetails.getCreatedBy(), internalUsersEntityRepository, externalUsersEntityRepository, logManager, log);
                                            if (onboarderDetailsResponse.isSuccess()) {
                                                String agentFullName = Helper.formatFullName(updateBAOnboardingDetails.getCrmFirstName(), updateBAOnboardingDetails.getCrmMiddleName(), updateBAOnboardingDetails.getCrmLastName());
                                                //  diferent sms templet for reverification and new onboarding

                                                String template = updateBAOnboardingDetails.getIsReverified() ? applicationStatusUpdatedReverifiedSMSContent : applicationStatusUpdateSMSContent;
                                                String content = MessageFormat.format(template, agentFullName, requestDto.getApplicationStatus());

                                                //   String content = MessageFormat.format(applicationStatusUpdateSMSContent, agentFullName, requestDto.getApplicationStatus());
                                                SmsRequestDto smsRequestDto = SmsRequestDto.builder()
                                                        .RequestRefID(transactionId)
                                                        .Receiver(List.of(et.safaricom.agent_onboarding.dto.request.api.SmsRequestDto.Receiver.builder().id(onboarderDetailsResponse.getData().getContactPhone()).build()))
                                                        .Content(content)
                                                        .build();

                                                apiGatewayService.sendSms(smsRequestDto);
                                            }

                                            return ResponseTemplate.success(null);
                                        }
                                        else { // Means it's Approved

                                            if (updateBAOnboardingDetails.getStatus().equals(APPLICATION_STATUS.PENDING)) {
                                                updateBAOnboardingDetails.setStatus(requestDto.getApplicationStatus());
                                              updateBAOnboardingDetails.setReasonForApplicationStatusUpdate(requestDto.getReasonForApplicationStatusUpdate());
                                                   if (requestDto.getRemark() != null && !requestDto.getRemark().isBlank()) {
                                                    updateBAOnboardingDetails.getBoRemarkList().add(requestDto.getRemark());
                                                    updateBAOnboardingDetails.setReasonForApplicationStatusUpdate(requestDto.getReasonForApplicationStatusUpdate());

                                                   }
                                                updateBAOnboardingDetails.setUpdatedBy(initiatorEmail);
                                               return handleCreatingTheAgenInDifferentChannels(updateBAOnboardingDetails, requestDto.getRemark(), initiatorEmail, logManager, log , transactionId);
                                            }
                                            else if (updateBAOnboardingDetails.getStatus().equals(APPLICATION_STATUS.REQUIRED)) {
                                                logManager.info(loggerCommon.getLog(log, "info", transactionId, 1, "There are still some fields missing! Please contact the onboarder to complete those first.", "Initiator [ " + initiatorEmail + " ] - Trying to approve an application [ " + updateBAOnboardingDetails.getId() + " ] with REQUIRED status."));
                                                return ResponseTemplate.error("There are still some fields missing! Please contact the onboarder to complete those first.");
                                            }
                                            else if (updateBAOnboardingDetails.getStatus().equals(APPLICATION_STATUS.REJECTED)) {
                                                logManager.info(loggerCommon.getLog(log, "info", transactionId, 1, "This application is already rejected before! Please re-onboard the user to approve.", "Initiator [ " + initiatorEmail + " ] - Trying to approve an application [ " + updateBAOnboardingDetails.getId() + " ] with REJECTED status."));
                                                return ResponseTemplate.error("This application is already rejected before! Please re-onboard the user to approve.");
                                            }
                                            else {
                                                logManager.info(loggerCommon.getLog(log, "info", transactionId, 1, "Unsupported application status for approval!", "Initiator [ " + initiatorEmail + " ] - Trying to approve an application [ " + updateBAOnboardingDetails.getId() + " ] with [ " + updateBAOnboardingDetails.getStatus() +  " ] status."));
                                                return ResponseTemplate.error("Unsupported application status for approval!");
                                            }
                                        }
                                    }
                                } else {
                                    return ResponseTemplate.error("Please specify the application status!");
                                }
                            }
                            else {
                                logManager.info(loggerCommon.getLog(log, "info", transactionId, 1, "Unauthorized!", "Trying to perform adjudication of someone else's agent. Assigned RSM [ " + updateBAOnboardingDetails.getRsm() + " ] - RAM [ " + updateBAOnboardingDetails.getRam() + " ] - Initiator [ " + initiatorEmail + " ] "));
                                return ResponseTemplate.error("Unauthorized!");
                            }
                        }
                        else {
                            logManager.info(loggerCommon.getLog(log, "info", transactionId,  1, "Agent not found!", "Specified ID [ " + requestDto.getSessionId() + " ] - Initiator [ " + initiatorEmail + " ] "));
                            return ResponseTemplate.error("Agent not found!");
                        }
                    }
                    else {
                        logManager.info(loggerCommon.getLog(log, "info",transactionId, 1, "Bad Request!", "ID is Missing on the Request! Initiator [ " + initiatorEmail + " ] "));
                        return ResponseTemplate.error("Bad Request!");
                    }
                }
                else {
                    logManager.info(loggerCommon.getLog(log, "info", transactionId,1, "Unauthorized!", "User by the specified username [ " + initiatorEmail + " ] with the roles [ " + userDetailsResponse.getData().getUserHierarchies() + " ] is not authorized!"));
                    return ResponseTemplate.error("Unauthorized!");
                }
            } else {
                return userDetailsResponse;
            }
        } catch (Exception e) {
            e.printStackTrace();
            logManager.error(loggerCommon.getLog(log,transactionId, 1, "Something went wrong! Please try again.", e));
            return ResponseTemplate.error("Something went wrong! Please try again.");
        }
    }

    private ResponseTemplate<?> performEdit(BAOnboardingUpdateRequestDto requestDto, LogManager logManager) {
        String transactionId = "ONE_PLATFORM_BA_ONBOARDING_" +UUID.randomUUID()+System.currentTimeMillis();

        Log log = new Log(transactionId, "info", "Performing an Adjudication", "", "", "", "ONE_PLATFORM_BA_ONBOARDING", "ONE_PLATFORM_BA_ONBOARDING",
                "0", "Default!", "Default Detailed Message.", "");

        try {
            String initiatorEmail = helper.extractEmailFromRequest(logManager, log, transactionId);

            ResponseTemplate<UserDetailsResponseDto> userDetailsResponse = commonService.getUserBasicDetails(null, initiatorEmail, internalUsersEntityRepository, externalUsersEntityRepository, logManager, log);
            if (userDetailsResponse.isSuccess()) {
                if (requestDto.getId() != null && requestDto.getId() > 0L) {
                    if (Helper.hasAccess(Authorization.CAN_EDIT_EX_PD_STA_WEB, userDetailsResponse.getData().getUserHierarchies()) || Helper.hasAccess(Authorization.CAN_EDIT_PD_WEB, userDetailsResponse.getData().getUserHierarchies())) {
                        Optional<BAOnboardingDetails> existingBAOnboardingDetailsOptional = baOnboardingDetailsRepository.findById(requestDto.getId());
                        if (existingBAOnboardingDetailsOptional.isPresent()) {
                            BAOnboardingDetails baOnboardingDetails = existingBAOnboardingDetailsOptional.get();
                            if (Helper.hasAccess(Authorization.CAN_EDIT_EX_PD_STA_WEB, userDetailsResponse.getData().getUserHierarchies())) {
                                if (requestDto.getDistributorId() != null && requestDto.getDistributorId() > 0L) {
                                    Optional<ChannelsEntity> distributorShopEntityOptional = channelsEntityRepository.findById(requestDto.getDistributorShopId());
                                    distributorShopEntityOptional.ifPresent(channelsEntity -> baOnboardingDetails.setDistributorShopId(channelsEntity.getId()));
                                }

                                baOnboardingDetails.setDivision(requestDto.getDivision() != null && !requestDto.getDivision().isBlank() ? requestDto.getDivision() : baOnboardingDetails.getDivision());
                                baOnboardingDetails.setRegion(requestDto.getRegion() != null && !requestDto.getRegion().isBlank() ? requestDto.getRegion() : baOnboardingDetails.getRegion());
                                baOnboardingDetails.setCluster(requestDto.getCluster() != null && !requestDto.getCluster().isBlank() ? requestDto.getCluster() : baOnboardingDetails.getCluster());
                                baOnboardingDetails.setRoute(requestDto.getRoute() != null && !requestDto.getRoute().isBlank() ? requestDto.getRoute() : baOnboardingDetails.getRoute());
                                baOnboardingDetails.setSite(requestDto.getSite() != null && !requestDto.getSite().isBlank() ? requestDto.getSite() : baOnboardingDetails.getSite());
                                baOnboardingDetails.setLatitude(requestDto.getLatitude() != null && requestDto.getLatitude() > 0.0 ? requestDto.getLatitude() : baOnboardingDetails.getLatitude());
                                baOnboardingDetails.setLongitude(requestDto.getLongitude() != null && requestDto.getLongitude() > 0.0 ? requestDto.getLongitude() : baOnboardingDetails.getLongitude());
                                baOnboardingDetails.setRsm(requestDto.getRsmUsername() != null && !requestDto.getRsmUsername().isBlank() ? requestDto.getRsmUsername() : baOnboardingDetails.getRsm());
                                baOnboardingDetails.setRam(requestDto.getRamUsername() != null && !requestDto.getRamUsername().isBlank() ? requestDto.getRamUsername() : baOnboardingDetails.getRam());
                                baOnboardingDetails.setReasonForUserUpdate(requestDto.getReasonForUserUpdate() != null && !requestDto.getReasonForUserUpdate().isBlank() ? requestDto.getReasonForUserUpdate() : baOnboardingDetails.getReasonForUserUpdate());

                            }

                            if (Helper.hasAccess(Authorization.CAN_EDIT_PD_WEB, userDetailsResponse.getData().getUserHierarchies())) {
                                if (requestDto.getDistributorId() != null && requestDto.getDistributorId() > 0L) {
                                    Optional<ChannelsEntity> distributorEntityOptional = channelsEntityRepository.findById(requestDto.getDistributorId());
                                    if (distributorEntityOptional.isEmpty()) {
                                        logManager.info(loggerCommon.getLog(log, "info", transactionId,1, "The Specified Distributor is either not active or not found.", "Distributor ID [ " + requestDto.getDistributorId() + " ] Initiator [ " + initiatorEmail + " ] "));
                                        return ResponseTemplate.error("The Specified Distributor is either not active or not found.");
                                    }
                                    baOnboardingDetails.setDistributorId(distributorEntityOptional.get().getId());
                                }
                            }

                            if (Helper.hasAccess(Authorization.CAN_EDIT_USERS_STATUS_WEB, userDetailsResponse.getData().getUserHierarchies())) {
                                baOnboardingDetails.setUserStatus(requestDto.getUserStatus() != null ? requestDto.getUserStatus() : baOnboardingDetails.getUserStatus());
                                if (requestDto.getUserStatus() != null && !requestDto.getUserStatus().equals(baOnboardingDetails.getUserStatus())) {
                                    baOnboardingDetails.setUserStatus(requestDto.getUserStatus());
                                    baOnboardingDetails.setReasonForUserUpdate(requestDto.getReasonForUserUpdate());
                                    ResponseTemplate<?> statusUpdateResponse = new ResponseTemplate<>();
                                    switch (requestDto.getUserStatus()) {
                                        case ACTIVE -> {
                                            statusUpdateResponse = updateUserStatusExternal(baOnboardingDetails, EKYC_USER_STATUS.AGENT_RESTORED, logManager, log ,transactionId);
                                        }
                                        case SUSPENDED -> {
                                            statusUpdateResponse = updateUserStatusExternal(baOnboardingDetails, EKYC_USER_STATUS.AGENT_SUSPENDED, logManager, log , transactionId);
                                        }
                                        case TERMINATED -> {
                                            statusUpdateResponse = updateUserStatusExternal(baOnboardingDetails, EKYC_USER_STATUS.AGENT_TERMINATED, logManager, log, transactionId);
                                        }
                                        default -> {
                                            statusUpdateResponse = ResponseTemplate.error("Very unlikely to happen!");
                                        }
                                    }
                                    if (!statusUpdateResponse.isSuccess()) {
                                        return ResponseTemplate.error("Something went wrong! Unable to update user status.");
                                    }
                                }
                            }

                            baOnboardingDetailsRepository.save(baOnboardingDetails);
                            return ResponseTemplate.success(null);
                        } else {
                            logManager.info(loggerCommon.getLog(log, "info", transactionId,1, "Agent not found!", "Existing BA User by the specified id [ " + requestDto.getId() + " ], not found! Initiator [ " + initiatorEmail + " ] "));
                            return ResponseTemplate.error("Agent not found!");
                        }
                    } else {
                        logManager.info(loggerCommon.getLog(log, "info", transactionId,1, "Unauthorized!", "User by the specified username [ " + initiatorEmail + " ] with the roles [ " + userDetailsResponse.getData().getUserHierarchies() + " ] is not authorized!"));
                        return ResponseTemplate.error("Unauthorized!");
                    }
                } else {
                    logManager.info(loggerCommon.getLog(log, "info", transactionId,1, "Bad Request!", "ID is Missing on the Request! Initiator [ " + initiatorEmail + " ] "));
                    return ResponseTemplate.error("Bad Request!");
                }
            } else {
                return userDetailsResponse;
            }
        } catch (Exception e) {
            e.printStackTrace();
            logManager.error(loggerCommon.getLog(log, transactionId,1, "Something went wrong! Please try again.", e));
            return ResponseTemplate.error("Something went wrong! Please try again.");
        }
    }

    private ResponseTemplate<?> updateUserStatusExternal(BAOnboardingDetails baOnboardingDetails, EKYC_USER_STATUS ekycUserStatus, LogManager logManager, Log log , String transactionId) {
        try {
            EKYCUpdateAgentApiRequestDto ekycUpdateAgentApiRequestDto = EKYCUpdateAgentApiRequestDto.builder()
                    .agentId(baOnboardingDetails.getAgentId())
                    .roles(List.of(baOnboardingDetails.getRoles().split(",")))
                    .setStatus(ekycUserStatus.getValue())
                    .timestamp(String.valueOf(System.currentTimeMillis()))
                    .build();
            ResponseTemplate<?> ekycUserUpdateResponse = apiGatewayService.eKycUpdateAgent(ekycUpdateAgentApiRequestDto, logManager, log);
            if (ekycUserUpdateResponse.isSuccess()) {
                ResponseTemplate<UserDetailsResponseDto> onboarderDetailsResponse = commonService.getUserBasicDetails(baOnboardingDetails.getCreatedById(), baOnboardingDetails.getCreatedBy(), internalUsersEntityRepository, externalUsersEntityRepository, logManager, log);
                kafkaProducerForAgentCreationAndUpdate(KAFKA_PRODUCER_COMMAND_ID.UPDATE_EXISTING_AGENT, baOnboardingDetails, onboarderDetailsResponse.isSuccess() ? onboarderDetailsResponse.getData().getContactPhone() : "N/A", false, logManager, log , transactionId);

                return ResponseTemplate.success("User status successfully updated!");
            }
            else {
                logManager.error(loggerCommon.getLog(log, "error", transactionId,1, "Unable to update user status on ekyc.", ekycUserUpdateResponse));
                return ResponseTemplate.error("Unable to update user status on ekyc.");
            }
        }
        catch (Exception e) {
            e.printStackTrace();
            logManager.error(loggerCommon.getLog(log,transactionId, 1, "Something went wrong! Please try again.", e));
            return ResponseTemplate.error("Something went wrong! Please try again.");
        }
    }

    private ResponseTemplate<?> handleCreatingTheAgenInDifferentChannels(BAOnboardingDetails updateBAOnboardingDetails, String lastRemark, String approvedBy, LogManager logManager, Log log, String transactionId) {
        String agentId = updateBAOnboardingDetails.getAgentId();
        if (updateBAOnboardingDetails.getAgentId() == null) { // Create on EKyc
            CreateAgentApiRequestDto createAgentApiRequestDto = CreateAgentApiRequestDto.builder()
                    .ekycId(updateBAOnboardingDetails.getCrmBiometricsId())
                    .timestamp(String.valueOf(System.currentTimeMillis()))
                    .build();

            ResponseTemplate<?> ekycAgentCreationResponse = ResponseTemplate.error("");
            agentId = Helper.generateAgentId(updateBAOnboardingDetails.getCrmFirstName(), updateBAOnboardingDetails.getCrmLastName());
            createAgentApiRequestDto.setAgentId(agentId);

            for (int i = 0; i < 150; i++) {
                Optional<BAOnboardingDetails> existingBaOnboardingDetailsByAgentIdOptional = baOnboardingDetailsRepository.findByAgentId(createAgentApiRequestDto.getAgentId());
                if (existingBaOnboardingDetailsByAgentIdOptional.isEmpty()) {
                    ekycAgentCreationResponse = apiGatewayService.eKycCreateAgent(createAgentApiRequestDto, logManager, log , transactionId) ;
                    if (ekycAgentCreationResponse.isSuccess() && ekycAgentCreationResponse.getStatus().equals(0)) {
                        break;
                    } else if (ekycAgentCreationResponse.isSuccess() && ekycAgentCreationResponse.getStatus().equals(2)) {

                        updateBAOnboardingDetails.setMsisdn(updateBAOnboardingDetails.getMsisdn() );
                        updateBAOnboardingDetails.setCrmBiometricsId(updateBAOnboardingDetails.getCrmBiometricsId());
                        ArrayList<String> existingBiometricIds = new ArrayList<>(updateBAOnboardingDetails.getBiometricsIdList());
                        updateBAOnboardingDetails.setBiometricsIdList(existingBiometricIds);
                        updateBAOnboardingDetails.setNidSubjectId( updateBAOnboardingDetails.getNidSubjectId());
                        updateBAOnboardingDetails.getBoRemarkList().add("An existing agent is found on ekyc!");
                        baOnboardingDetailsRepository.save(updateBAOnboardingDetails);
                        break;
                    } else {
                        createAgentApiRequestDto.setAgentId(agentId + Helper.getRandomNumber());
                    }
                } else {
                    createAgentApiRequestDto.setAgentId(agentId + Helper.getRandomNumber());
                }
            }

            if (ekycAgentCreationResponse.isSuccess()) {
                updateBAOnboardingDetails.setAgentId(createAgentApiRequestDto.getAgentId());
                updateBAOnboardingDetails.setIsAgentCreatedOnEkyc(true);


                // Create on DxL
                DxLEkycRequestDto dxLEkycRequestDto = DxLEkycRequestDto.builder()
                   .requestRefID(log.getTransactionID())
                    .channelSessionID(log.getTransactionID())
                    .remark(lastRemark)
                    .referenceData(List.of(DxLEkycRequestDto.ReferenceData.builder().key("Remark").value(lastRemark).build()))
                    .initiator(DxLEkycRequestDto.Initiator.builder().identifierType("12").identifier(createAgentApiRequestDto.getAgentId()).build())
                    .receiverParty(DxLEkycRequestDto.ReceiverParty.builder().identifierType(1).identifier(updateBAOnboardingDetails.getMsisdn()).build())
                    .parameters(List.of(
                            DxLEkycRequestDto.Parameter.builder().key("operator_id").value("SFC").build(),
//                            DxLEkycRequestDto.Parameter.builder().key("short_code").value("20181").build(),
                            DxLEkycRequestDto.Parameter.builder().key("first_name").value(updateBAOnboardingDetails.getCrmFirstName()).build(),
                            DxLEkycRequestDto.Parameter.builder().key("last_name").value(updateBAOnboardingDetails.getCrmLastName()).build(),
//                            DxLEkycRequestDto.Parameter.builder().key("parent_code").value("01").build(),
                            DxLEkycRequestDto.Parameter.builder().key("user_type").value("USER").build(),
                            DxLEkycRequestDto.Parameter.builder().key("parent_name").value("Parent").build(),
                            DxLEkycRequestDto.Parameter.builder().key("area_sales_manager").value(approvedBy).build(),
                            DxLEkycRequestDto.Parameter.builder().key("region").value(updateBAOnboardingDetails.getRegion()).build(),
                            DxLEkycRequestDto.Parameter.builder().key("sales_area").value(updateBAOnboardingDetails.getSite()).build(),
                            DxLEkycRequestDto.Parameter.builder().key("cluster").value(updateBAOnboardingDetails.getCluster()).build()
                    ))
                    .build();

                ResponseTemplate<?> dxlEkycAgentCreationResponse = apiGatewayService.dxlEKycCreateAgent(dxLEkycRequestDto, logManager, log , transactionId);
                if (dxlEkycAgentCreationResponse.isSuccess()) {
                    updateBAOnboardingDetails.setIsAgentCreatedOnDxl(true);
                }

                updateBAOnboardingDetails.setCreatedOn(LocalDateTime.now());
                ResponseTemplate<UserDetailsResponseDto> onboarderDetailsResponse = commonService.getUserBasicDetails(updateBAOnboardingDetails.getCreatedById(), updateBAOnboardingDetails.getCreatedBy(), internalUsersEntityRepository, externalUsersEntityRepository, logManager, log);

                kafkaProducerForAgentCreationAndUpdate(KAFKA_PRODUCER_COMMAND_ID.CREATE_NEW_AGENT, updateBAOnboardingDetails, onboarderDetailsResponse.isSuccess() ? onboarderDetailsResponse.getData().getContactPhone() : "N/A", false, logManager, log , transactionId);

                System.err.println("[BD] Kafka Creation Status: " + updateBAOnboardingDetails.getIsAgentCreatedOnBD());
                updateBAOnboardingDetails.setApprovedBy(approvedBy);
                updateBAOnboardingDetails.setUserStatus(USER_STATUS.ACTIVE);
                updateBAOnboardingDetails.setApprovedOn(LocalDateTime.now());
                baOnboardingDetailsRepository.save(updateBAOnboardingDetails);

                if (onboarderDetailsResponse.isSuccess()) {
                    String agentFullName = Helper.formatFullName(updateBAOnboardingDetails.getCrmFirstName(), updateBAOnboardingDetails.getCrmMiddleName(), updateBAOnboardingDetails.getCrmLastName());
                    //sms for reverification and new onboarding
                    String statusUpdateTemplate = updateBAOnboardingDetails.getIsReverified() ? applicationStatusUpdatedReverifiedSMSContent : applicationStatusUpdateSMSContent;
                    String content = MessageFormat.format(statusUpdateTemplate, agentFullName, updateBAOnboardingDetails.getStatus());

                    // String content = MessageFormat.format(applicationStatusUpdateSMSContent, agentFullName, updateBAOnboardingDetails.getStatus());
                    SmsRequestDto smsRequestDto = SmsRequestDto.builder()
                            .RequestRefID(log.getTransactionID())
                            .Receiver(List.of(et.safaricom.agent_onboarding.dto.request.api.SmsRequestDto.Receiver.builder().id(onboarderDetailsResponse.getData().getContactPhone()).build()))
                            .Content(content)
                            .build();

                    apiGatewayService.sendSms(smsRequestDto);
                    //Approval sms for reverification and new onbording
                    String approvalTemplate = updateBAOnboardingDetails.getIsReverified() ? agentApplicationStatusReverificationSMSContent : agentApplicationStatusApprovedSMSContent;
                    content = MessageFormat.format(approvalTemplate, agentFullName, "\n", updateBAOnboardingDetails.getAgentId());

                    // content = MessageFormat.format(agentApplicationStatusApprovedSMSContent, agentFullName, "\n", updateBAOnboardingDetails.getAgentId());
                    smsRequestDto = SmsRequestDto.builder()
                            .RequestRefID(log.getTransactionID() + "_2")
                            .Receiver(List.of(et.safaricom.agent_onboarding.dto.request.api.SmsRequestDto.Receiver.builder().id(updateBAOnboardingDetails.getMsisdn()).build()))
                            .Content(content)
                            .build();

                    apiGatewayService.sendSms(smsRequestDto);
                }
                return ResponseTemplate.success(null);
            }
            else {
                logManager.error(loggerCommon.getLog(log, "error", transactionId,1, "Unable to complete approval! Please contact support.", "Unable to create an agent on EKYC! Approving attempted by [ " + approvedBy + " ] \n" + ekycAgentCreationResponse));
                return ResponseTemplate.error("Unable to complete approval! Please contact support.");
            }
        }
        else {

            boolean needsEkycCreation = !updateBAOnboardingDetails.getIsAgentCreatedOnEkyc();
            boolean needsDxlCreation = !updateBAOnboardingDetails.getIsAgentCreatedOnDxl();


            boolean alreadyExistsOnEkyc = updateBAOnboardingDetails.getBoRemarkList() != null &&
                    updateBAOnboardingDetails.getBoRemarkList().stream()
                            .anyMatch(remark -> remark.contains("An existing agent is found on ekyc!"));

            if (alreadyExistsOnEkyc && !updateBAOnboardingDetails.getIsAgentCreatedOnEkyc()) {
                updateBAOnboardingDetails.setIsAgentCreatedOnEkyc(true);
                needsEkycCreation = false;
            }


            if (needsEkycCreation) {

                CreateAgentApiRequestDto createAgentApiRequestDto = CreateAgentApiRequestDto.builder()
                        .ekycId(updateBAOnboardingDetails.getCrmBiometricsId())
                        .agentId(updateBAOnboardingDetails.getAgentId())
                        .timestamp(String.valueOf(System.currentTimeMillis()))
                        .roles(List.of(eKycAgentEKycRole, eKycAgentMPESAEKycRole))
                        .build();

                ResponseTemplate<?> ekycAgentCreationResponse = apiGatewayService.eKycCreateAgent(createAgentApiRequestDto, logManager, log , transactionId);

                if (ekycAgentCreationResponse.isSuccess()) {
                    if (ekycAgentCreationResponse.getStatus().equals(2)) {
                        updateBAOnboardingDetails.setMsisdn(updateBAOnboardingDetails.getMsisdn() );
                        updateBAOnboardingDetails.setCrmBiometricsId(updateBAOnboardingDetails.getCrmBiometricsId());
                        ArrayList<String> existingBiometricIds = new ArrayList<>(updateBAOnboardingDetails.getBiometricsIdList());
                        updateBAOnboardingDetails.setBiometricsIdList(existingBiometricIds);
                        updateBAOnboardingDetails.setNidSubjectId( updateBAOnboardingDetails.getNidSubjectId());
                        updateBAOnboardingDetails.getBoRemarkList().add("An existing agent is found on ekyc!");
                        updateBAOnboardingDetails.setIsAgentCreatedOnEkyc(true);
                        baOnboardingDetailsRepository.save(updateBAOnboardingDetails);
                    } else {
                        updateBAOnboardingDetails.setIsAgentCreatedOnEkyc(true);
                    }
                }
            }


            if (needsDxlCreation) {
                DxLEkycRequestDto dxLEkycRequestDto = DxLEkycRequestDto.builder()
                       .requestRefID(log.getTransactionID())
                        .channelSessionID(log.getTransactionID())
                        .remark(lastRemark)
                        .referenceData(List.of(DxLEkycRequestDto.ReferenceData.builder().key("Remark").value(lastRemark).build()))
                        .initiator(DxLEkycRequestDto.Initiator.builder().identifierType("12").identifier(updateBAOnboardingDetails.getAgentId()).build())
                        .receiverParty(DxLEkycRequestDto.ReceiverParty.builder().identifierType(1).identifier(updateBAOnboardingDetails.getMsisdn()).build())
                        .parameters(List.of(
                                DxLEkycRequestDto.Parameter.builder().key("operator_id").value("SFC").build(),
                                DxLEkycRequestDto.Parameter.builder().key("first_name").value(updateBAOnboardingDetails.getCrmFirstName()).build(),
                                DxLEkycRequestDto.Parameter.builder().key("last_name").value(updateBAOnboardingDetails.getCrmLastName()).build(),
                                DxLEkycRequestDto.Parameter.builder().key("user_type").value("USER").build(),
                                DxLEkycRequestDto.Parameter.builder().key("parent_name").value("Parent").build(),
                                DxLEkycRequestDto.Parameter.builder().key("area_sales_manager").value(approvedBy).build(),
                                DxLEkycRequestDto.Parameter.builder().key("region").value(updateBAOnboardingDetails.getRegion()).build(),
                                DxLEkycRequestDto.Parameter.builder().key("sales_area").value(updateBAOnboardingDetails.getSite()).build(),
                                DxLEkycRequestDto.Parameter.builder().key("cluster").value(updateBAOnboardingDetails.getCluster()).build()
                        ))
                        .build();

                ResponseTemplate<?> dxlEkycAgentCreationResponse = apiGatewayService.dxlEKycCreateAgent(dxLEkycRequestDto, logManager, log , transactionId);
                if (dxlEkycAgentCreationResponse.isSuccess()) {
                    updateBAOnboardingDetails.setIsAgentCreatedOnDxl(true);
                }
            }
        }

                // Only proceed with approval if both EKyc and DxL creations are successful
                if (updateBAOnboardingDetails.getIsAgentCreatedOnEkyc() && updateBAOnboardingDetails.getIsAgentCreatedOnDxl()) {
                    updateBAOnboardingDetails.setUpdatedOn(LocalDateTime.now());

                    ResponseTemplate<UserDetailsResponseDto> onboarderDetailsResponse = commonService.getUserBasicDetails(updateBAOnboardingDetails.getCreatedById(), updateBAOnboardingDetails.getCreatedBy(), internalUsersEntityRepository, externalUsersEntityRepository, logManager, log);

                    kafkaProducerForAgentCreationAndUpdate(KAFKA_PRODUCER_COMMAND_ID.UPDATE_EXISTING_AGENT, updateBAOnboardingDetails, onboarderDetailsResponse.isSuccess() ? onboarderDetailsResponse.getData().getContactPhone() : "N/A", true, logManager, log, transactionId);

                    System.err.println("[BD] Kafka Update Status: " + updateBAOnboardingDetails.getIsAgentCreatedOnBD());
                    updateBAOnboardingDetails.setApprovedBy(approvedBy);
                    updateBAOnboardingDetails.setUserStatus(USER_STATUS.ACTIVE);
                    updateBAOnboardingDetails.setApprovedOn(LocalDateTime.now());
                    baOnboardingDetailsRepository.save(updateBAOnboardingDetails);

                    if (onboarderDetailsResponse.isSuccess()) {
                        String agentFullName = Helper.formatFullName(updateBAOnboardingDetails.getCrmFirstName(), updateBAOnboardingDetails.getCrmMiddleName(), updateBAOnboardingDetails.getCrmLastName());
                        String content = MessageFormat.format(applicationStatusUpdateSMSContent, agentFullName, updateBAOnboardingDetails.getStatus());
                        SmsRequestDto smsRequestDto = SmsRequestDto.builder()
                                .RequestRefID(log.getTransactionID())
                                .Receiver(List.of(et.safaricom.agent_onboarding.dto.request.api.SmsRequestDto.Receiver.builder().id(onboarderDetailsResponse.getData().getContactPhone()).build()))
                                .Content(content)
                                .build();

                        apiGatewayService.sendSms(smsRequestDto);

                        content = MessageFormat.format(agentApplicationStatusApprovedSMSContent, agentFullName, "\n", updateBAOnboardingDetails.getAgentId());
                        smsRequestDto = SmsRequestDto.builder()
                                .RequestRefID(log.getTransactionID() + "_2")
                                .Receiver(List.of(et.safaricom.agent_onboarding.dto.request.api.SmsRequestDto.Receiver.builder().id(updateBAOnboardingDetails.getMsisdn()).build()))
                                .Content(content)
                                .build();

                        apiGatewayService.sendSms(smsRequestDto);
                    }
                    return ResponseTemplate.success(null);
                } else {

                    updateBAOnboardingDetails.setUpdatedOn(LocalDateTime.now());
                    baOnboardingDetailsRepository.save(updateBAOnboardingDetails);
                    return ResponseTemplate.error("Agent creation is not complete on all systems. Please try again.");
                }
            }


    private void kafkaProducerForAgentCreationAndUpdate(KAFKA_PRODUCER_COMMAND_ID kafkaProducerCommandId, BAOnboardingDetails updateBAOnboardingDetails, String onboarderContactPhone, Boolean isReVerified, LogManager logManager, Log log , String transactionId) {
        CreateAgentOnBDKafkaRequestDto kafkaMessageDto = CreateAgentOnBDKafkaRequestDto.builder()
                .commandId(kafkaProducerCommandId)
                .isReVerified(isReVerified)
                .content(
                        CreateAgentOnBDKafkaRequestDto.Content.builder()
                                .nidFirstName(updateBAOnboardingDetails.getNidFirstName())
                                .nidMiddleName(updateBAOnboardingDetails.getNidMiddleName())
                                .nidLastName(updateBAOnboardingDetails.getNidLastName())
                                .crmFullName(Helper.formatFullName(updateBAOnboardingDetails.getCrmFirstName(), updateBAOnboardingDetails.getCrmMiddleName(), updateBAOnboardingDetails.getCrmLastName()))
                                .crmBioMetricsId(updateBAOnboardingDetails.getCrmBiometricsId())
                                .agentId(updateBAOnboardingDetails.getAgentId())
                                .msisdn(updateBAOnboardingDetails.getMsisdn())
                                .contactPhone(onboarderContactPhone)
                                .division(updateBAOnboardingDetails.getDivision())
                                .region(updateBAOnboardingDetails.getRegion())
                                .cluster(updateBAOnboardingDetails.getCluster())
                                .route(updateBAOnboardingDetails.getRoute())
                                .site(updateBAOnboardingDetails.getSite())
                                .address(
                                        (updateBAOnboardingDetails.getNidAddress() != null) ? CreateAgentOnBDKafkaRequestDto.Content.Address.builder()
                                                .kebele(updateBAOnboardingDetails.getNidAddress().getKebele())
                                                .nidRegion(updateBAOnboardingDetails.getNidAddress().getNidRegion())
                                                .woreda(updateBAOnboardingDetails.getNidAddress().getWoreda())
                                                .zone(updateBAOnboardingDetails.getNidAddress().getZone())
                                                .build() : CreateAgentOnBDKafkaRequestDto.Content.Address.builder()
                                                .kebele("N/A")
                                                .nidRegion("N/A")
                                                .woreda("N/A")
                                                .zone("N/A")
                                                .build()
                                )
                                .rsm(updateBAOnboardingDetails.getRsm())
                                .ram(updateBAOnboardingDetails.getRam())
                                .updatedOn(updateBAOnboardingDetails.getUpdatedOn() != null ? updateBAOnboardingDetails.getUpdatedOn().toString() : null)
                                .createdOn(updateBAOnboardingDetails.getCreatedOn() != null ? updateBAOnboardingDetails.getCreatedOn().toString() : null)
                                .roles(Arrays.stream(updateBAOnboardingDetails.getRoles().split(",")).toList())
                                .build()
                )
                .build();

        try {
            kafkaProducerService.sendMessage(kafkaMessageDto, logManager, log);
            updateBAOnboardingDetails.setIsAgentCreatedOnBD(true);
        } catch (Exception e) {
            System.err.println(e.getMessage());
        }

        log.setTargetSystem("ONE_PLATFORM_BA_ONBOARDING");
        logManager.info(loggerCommon.getLog(log, "info",transactionId, 0, "Kafka Message Produced!", kafkaMessageDto));
    }

    public ResponseTemplate<?> triggerSendKafkaMessageTest(Long id, LogManager logManager, Log log) {
        Optional<BAOnboardingDetails> updateBAOnboardingDetailsOptional = baOnboardingDetailsRepository.findById(id);
        if (updateBAOnboardingDetailsOptional.isEmpty()) {
            return ResponseTemplate.error("Empty");
        }

        BAOnboardingDetails updateBAOnboardingDetails = updateBAOnboardingDetailsOptional.get();
        CreateAgentOnBDKafkaRequestDto kafkaMessageDto = CreateAgentOnBDKafkaRequestDto.builder()
                .commandId(KAFKA_PRODUCER_COMMAND_ID.CREATE_NEW_AGENT)
                .content(
                        CreateAgentOnBDKafkaRequestDto.Content.builder()
                                .nidFirstName(updateBAOnboardingDetails.getNidFirstName())
                                .nidMiddleName(updateBAOnboardingDetails.getNidMiddleName())
                                .nidLastName(updateBAOnboardingDetails.getNidLastName())
                                .crmFullName(Helper.formatFullName(updateBAOnboardingDetails.getCrmFirstName(), updateBAOnboardingDetails.getCrmMiddleName(), updateBAOnboardingDetails.getCrmLastName()))
                                .crmBioMetricsId(updateBAOnboardingDetails.getCrmBiometricsId())
                                .agentId(updateBAOnboardingDetails.getAgentId())
                                .msisdn(updateBAOnboardingDetails.getMsisdn())
                                .division(updateBAOnboardingDetails.getDivision())
                                .region(updateBAOnboardingDetails.getRegion())
                                .cluster(updateBAOnboardingDetails.getCluster())
                                .route(updateBAOnboardingDetails.getRoute())
                                .site(updateBAOnboardingDetails.getSite())
                                .address(
                                        (updateBAOnboardingDetails.getNidAddress() != null) ? CreateAgentOnBDKafkaRequestDto.Content.Address.builder()
                                                .kebele(updateBAOnboardingDetails.getNidAddress().getKebele())
                                                .nidRegion(updateBAOnboardingDetails.getNidAddress().getNidRegion())
                                                .woreda(updateBAOnboardingDetails.getNidAddress().getWoreda())
                                                .zone(updateBAOnboardingDetails.getNidAddress().getZone())
                                                .build() : CreateAgentOnBDKafkaRequestDto.Content.Address.builder()
                                                .kebele("N/A")
                                                .nidRegion("N/A")
                                                .woreda("N/A")
                                                .zone("N/A")
                                                .build()
                                )
                                .rsm(updateBAOnboardingDetails.getRsm())
                                .ram(updateBAOnboardingDetails.getRam())
                                .roles(Arrays.stream(updateBAOnboardingDetails.getRoles().split(",")).toList())
                                .build()
                )
                .build();

        try {
            kafkaProducerService.sendMessage(kafkaMessageDto, logManager, log);
        } catch (Exception e) {
            System.err.println(e.getMessage());
        }

        return ResponseTemplate.success(updateBAOnboardingDetails);
    }

    public ResponseTemplate<?> listUsersRouter(String userAgent, ListUsersFilterParamsRequestDto filteringCriteria, LogManager logManager) {
        System.out.println("User List -- User-Agent: " + userAgent);
        String source = Helper.identifyRequestSource(userAgent);
        System.out.println("User List -- Source: " + source);

        switch (source) {
            case "MOBILE_APP" -> {
                return listUsersAppHandler(filteringCriteria, logManager);
            }
            case "WEB" -> {
                return listUsersWebHandler(filteringCriteria, logManager);
            }
            default -> {
                return ResponseTemplate.error("Bad Request!");
            }
        }
    }

    private ResponseTemplate<?> listUsersAppHandler(ListUsersFilterParamsRequestDto filteringCriteria, LogManager logManager) {
        String transactionId = "ONE_PLATFORM_BA_ONBOARDING_" + UUID.randomUUID()+ System.currentTimeMillis();

        Log log = new Log(transactionId, "info", "Fetching onboarded agents on app", "", "", "", "ONE_PLATFORM_BA_ONBOARDING", "ONE_PLATFORM_BA_ONBOARDING",
                "0", "Default!", "Default Detailed Message.", "");

        try {
            String initiatorEmail = helper.extractEmailFromRequest(logManager, log , transactionId);

            ResponseTemplate<UserDetailsResponseDto> userDetailsResponse = commonService.getUserBasicDetails(null, initiatorEmail, internalUsersEntityRepository, externalUsersEntityRepository, logManager, log);
            if (userDetailsResponse.isSuccess()) {
                if (Helper.hasAccess(Authorization.CAN_VIEW_CREATED_USERS_LIST_APP, userDetailsResponse.getData().getUserHierarchies())) {
                    if (filteringCriteria.getMsisdn() != null && !filteringCriteria.getMsisdn().isBlank()) {
                        ResponseTemplate<String> validatedPhoneNumberResponse = helper.validatePhoneNumber(filteringCriteria.getMsisdn());
                        if (!validatedPhoneNumberResponse.isSuccess()) {
                            return validatedPhoneNumberResponse;
                        }
                        filteringCriteria.setMsisdn(validatedPhoneNumberResponse.getData());
                    }

//                    Note:: ONBOARDER - created by
                    if (Helper.hasAccess(Authorization.CAN_ONBOARD_APP, userDetailsResponse.getData().getUserHierarchies())) {
                        if (filteringCriteria.getCreatedByList() == null || filteringCriteria.getCreatedByList().isEmpty()) {
                            filteringCriteria.setCreatedByList(List.of(initiatorEmail));
                        }
                    }
//                    Note:: RSM - approve
                    if (userDetailsResponse.getData().getUserHierarchies().contains(Constants.DISTRIBUTION_RSM_ROLE_ID)) {
                        filteringCriteria.setRsmList(List.of(initiatorEmail));
                    }
//                    Note:: RAM - approve
                    else if (userDetailsResponse.getData().getUserHierarchies().contains(Constants.RAM_ROLE_ID)) {
                        filteringCriteria.setRam(initiatorEmail);
                    }
//                    Note:: NSM - RSM - Internal User
                    else if (userDetailsResponse.getData().getUserHierarchies().contains(Constants.DISTRIBUTION_NSM_ROLE_ID)) {
                        List<InternalUsersEntity> myRSMUsers = internalUsersEntityRepository.findAllByParentId(userDetailsResponse.getData().getId());
                        filteringCriteria.setRsmList(myRSMUsers.stream().map(InternalUsersEntity::getUserName).collect(Collectors.toSet()).stream().toList());
                    }
//                    Note:: Distributor - Shop Manager - External User
                    else if (userDetailsResponse.getData().getUserHierarchies().contains(Constants.DISTRIBUTION_ADMIN_ID)) {
                        List<ExternalUsersEntity> myDSMUsers = externalUsersEntityRepository.findAllByParentId(userDetailsResponse.getData().getId());
                        filteringCriteria.setCreatedByList(myDSMUsers.stream().map(ExternalUsersEntity::getUserName).collect(Collectors.toSet()).stream().toList());
                    }

                    System.err.println("[APP] Filtering criteria: " + filteringCriteria);
                    Specification<BAOnboardingDetails> spec = SpecificationBuilder.buildStoreSpecification(filteringCriteria, Map.of("channel", "app"));
                    Pageable pageable = PageRequest.of(filteringCriteria.getPage(), filteringCriteria.getSize());
                    Page<BAOnboardingDetails> baOnboardingDetailsPage = baOnboardingDetailsRepository.findAll(spec, pageable);

                    Map<String, Object> responsePageable = Helper.getPageableResponse(baOnboardingDetailsPage);
                    List<LinkedHashMap<String, Object>> responseContentList =  baOnboardingDetailsPage.getContent().stream().map(ba -> {
                                LinkedHashMap<String, Object> response = new LinkedHashMap<>();
                                response.put("id", ba.getId());
                                response.put("sessionId", ba.getSessionId());
                                response.put("fullName", Helper.formatFullName(ba.getCrmFirstName(), ba.getCrmMiddleName(), ba.getCrmLastName()));
                                response.put("applicationStatus", ba.getStatus());
                                response.put("userStatus", ba.getUserStatus());
                                response.put("agentId", ba.getAgentId() != null ? ba.getAgentId() : "N/A");
                                response.put("msisdn", ba.getMsisdn());
                                response.put("isNew", ba.getIsReverified());

                        return response;
                            }
                    ).toList();

                    responsePageable.put("content", responseContentList);

                    return ResponseTemplate.success(responsePageable);
                }
                else {
                    logManager.info(loggerCommon.getLog(log, "info", transactionId, 1, "Unauthorized!", "User by the specified username [ " + initiatorEmail + " ] with the roles [ " + userDetailsResponse.getData().getUserHierarchies() + " ] is not authorized!"));
                    return ResponseTemplate.error("Unauthorized!");
                }
            } else {
                return userDetailsResponse;
            }
        } catch (Exception e) {
            e.printStackTrace();
            logManager.error(loggerCommon.getLog(log, transactionId,  1, "Something went wrong! Please try again.", e));
            return ResponseTemplate.error("Something went wrong! Please try again.");
        }
    }

    private ResponseTemplate<?> listUsersWebHandler(ListUsersFilterParamsRequestDto filteringCriteria, LogManager logManager) {
        String transactionId = "ONE_PLATFORM_BA_ONBOARDING_" + System.currentTimeMillis();

        Log log = new Log(transactionId, "info", "Fetching onboarded agents on web", "", "", "", "ONE_PLATFORM_BA_ONBOARDING", "ONE_PLATFORM_BA_ONBOARDING",
                "0", "Default!", "Default Detailed Message.", "");

        try {
            String initiatorEmail = helper.extractEmailFromRequest(logManager, log , transactionId);

            ResponseTemplate<UserDetailsResponseDto> userDetailsResponse = commonService.getUserBasicDetails(null, initiatorEmail, internalUsersEntityRepository, externalUsersEntityRepository, logManager, log);
            if (userDetailsResponse.isSuccess()) {
                if (Helper.hasAccess(Authorization.CAN_VIEW_ALL_STATUS_USERS_WEB, userDetailsResponse.getData().getUserHierarchies())) {
                    if (filteringCriteria.getMsisdn() != null && !filteringCriteria.getMsisdn().isBlank()) {
                        ResponseTemplate<String> validatedPhoneNumberResponse = helper.validatePhoneNumber(filteringCriteria.getMsisdn());
                        if (!validatedPhoneNumberResponse.isSuccess()) {
                            return validatedPhoneNumberResponse;
                        }
                        filteringCriteria.setMsisdn(validatedPhoneNumberResponse.getData());
                    }

//                    Note:: ONBOARDER - created by
                    if (Helper.hasAccess(Authorization.CAN_ONBOARD_APP, userDetailsResponse.getData().getUserHierarchies())) {
                        if (filteringCriteria.getCreatedByList() == null || filteringCriteria.getCreatedByList().isEmpty()) {
                            filteringCriteria.setCreatedByList(List.of(initiatorEmail));
                        }
                    }
//                    Note:: RSM - approve
                    if (userDetailsResponse.getData().getUserHierarchies().contains(Constants.DISTRIBUTION_RSM_ROLE_ID)) {
                        filteringCriteria.setRsmList(List.of(initiatorEmail));
                    }
////                    Note:: RAM - approve
                    else if (userDetailsResponse.getData().getUserHierarchies().contains(Constants.RAM_ROLE_ID)) {
                        filteringCriteria.setRamList(List.of(initiatorEmail));
                    }
//                    Note:: NSM - RSM - Internal User
                    else if (userDetailsResponse.getData().getUserHierarchies().contains(Constants.DISTRIBUTION_NSM_ROLE_ID)) {
                        List<InternalUsersEntity> myRSMUsers = internalUsersEntityRepository.findAllByParentId(userDetailsResponse.getData().getId());
                        filteringCriteria.setRsmList(myRSMUsers.stream().map(InternalUsersEntity::getUserName).collect(Collectors.toSet()).stream().toList());
                    }
//                    Note:: Distributor - Shop Manager - External User
                    else if (userDetailsResponse.getData().getUserHierarchies().contains(Constants.DISTRIBUTION_ADMIN_ID)) {
                        List<ExternalUsersEntity> myDSMUsers = externalUsersEntityRepository.findAllByParentId(userDetailsResponse.getData().getId());
                        filteringCriteria.setCreatedByList(myDSMUsers.stream().map(ExternalUsersEntity::getUserName).collect(Collectors.toSet()).stream().toList());
                    }

                    System.err.println("[WEB] Filtering criteria: " + filteringCriteria);
                    Specification<BAOnboardingDetails> spec = SpecificationBuilder.buildStoreSpecification(filteringCriteria, Map.of("channel", "web"));
                    Pageable pageable = PageRequest.of(filteringCriteria.getPage(), filteringCriteria.getSize());
                    Page<BAOnboardingDetails> baOnboardingDetailsPage = baOnboardingDetailsRepository.findAll(spec, pageable);

                    Map<String, Object> responsePageable = Helper.getPageableResponse(baOnboardingDetailsPage);
                    Set<Long> distributorIdList = baOnboardingDetailsPage.getContent().stream().map(BAOnboardingDetails::getDistributorId).collect(Collectors.toSet());
                    List<ChannelsEntity> distributors = channelsEntityRepository.findAllChannelsByMultipleIds(distributorIdList);
                    List<LinkedHashMap<String, Object>> responseContentList = baOnboardingDetailsPage.getContent().stream().map(ba -> {
                                String distributorName = distributors.stream().filter(dis -> dis.getId().equals(ba.getDistributorId())).findFirst().map(ChannelsEntity::getChannelName).orElse("N/A");
                                LinkedHashMap<String, Object> response = new LinkedHashMap<>();
                                response.put("id", ba.getId());
                                response.put("fullName", Helper.formatFullName(ba.getCrmFirstName(), ba.getCrmMiddleName(), ba.getCrmLastName()));
                                response.put("applicationStatus", ba.getStatus());
                                response.put("userStatus", ba.getUserStatus());
                                response.put("agentId", ba.getAgentId() != null ? ba.getAgentId() : "N/A");
                                response.put("msisdn", ba.getMsisdn());
                                response.put("parentDistributor", distributorName);
                                response.put("createdOn", ba.getCreatedOn());
                                response.put("division", ba.getDivision());
                                response.put("region", ba.getRegion());
                                response.put("isNew", ba.getIsReverified());
                                return response;
                            }
                    ).toList();

                    responsePageable.put("content", responseContentList);

                    return ResponseTemplate.success(responsePageable);
                }
                else {
                    logManager.info(loggerCommon.getLog(log, "info", transactionId, 1, "Unauthorized!", "User by the specified username [ " + initiatorEmail + " ] with the roles [ " + userDetailsResponse.getData().getUserHierarchies() + " ] is not authorized!"));
                    return ResponseTemplate.error("Unauthorized!");
                }
            } else {
                return userDetailsResponse;
            }
        } catch (Exception e) {
            e.printStackTrace();
            logManager.error(loggerCommon.getLog(log,transactionId,  1, "Something went wrong! Please try again.", e));
            return ResponseTemplate.error("Something went wrong! Please try again.");
        }
    }

    public ResponseTemplate<?> userDetailRouter(String userAgent, Long id, LogManager logManager) {
        System.out.println("User Detail -- User-Agent: " + userAgent);
        String source = Helper.identifyRequestSource(userAgent);
        System.out.println("User Detail -- Source: " + source);

        switch (source) {
            case "MOBILE_APP" -> {
                return userDetailAppHandler(id, logManager);
            }
            case "WEB" -> {
                return userDetailWebHandler(id, logManager);
            }
            default -> {
                return ResponseTemplate.error("Bad Request!");
            }
        }
    }

    private ResponseTemplate<?> userDetailAppHandler(Long id, LogManager logManager) {
        String transactionId = "ONE_PLATFORM_BA_ONBOARDING_" + System.currentTimeMillis();

        Log log = new Log(transactionId, "info", "Fetching onboarded agents on web", "", "", "", "ONE_PLATFORM_BA_ONBOARDING", "ONE_PLATFORM_BA_ONBOARDING",
                "0", "Default!", "Default Detailed Message.", "");

        try {
            String initiatorEmail = helper.extractEmailFromRequest(logManager, log , transactionId);

            ResponseTemplate<UserDetailsResponseDto> userDetailsResponse = commonService.getUserBasicDetails(null, initiatorEmail, internalUsersEntityRepository, externalUsersEntityRepository, logManager, log);
            if (userDetailsResponse.isSuccess()) {
                if (Helper.hasAccess(Authorization.CAN_VIEW_ALL_STATUS_USERS_APP, userDetailsResponse.getData().getUserHierarchies())) {
                    Optional<BAOnboardingDetails> baOnboardingDetailsOptional = baOnboardingDetailsRepository.findById(id);
                    if (baOnboardingDetailsOptional.isPresent()) {
                        return ResponseTemplate.success(agentDetailsAppResponseBuilder(baOnboardingDetailsOptional.get()));
                    }
                    else {
                        logManager.info(loggerCommon.getLog(log, "info", transactionId, 1, "Agent not found!", "User by the specified id [ " + id + " ], requested by [ " + initiatorEmail + " ], is not found!"));
                        return ResponseTemplate.error("Agent not found!");
                    }
                }
                else {
                    logManager.info(loggerCommon.getLog(log, "info",transactionId,  1, "Unauthorized!", "User by the specified username [ " + initiatorEmail + " ] with the roles [ " + userDetailsResponse.getData().getUserHierarchies() + " ] is not authorized!"));
                    return ResponseTemplate.error("Unauthorized!");
                }
            } else {
                return userDetailsResponse;
            }
        } catch (Exception e) {
            e.printStackTrace();
            logManager.error(loggerCommon.getLog(log, transactionId, 1, "Something went wrong! Please try again.", e));
            return ResponseTemplate.error("Something went wrong! Please try again.");
        }
    }

    private ResponseTemplate<?> userDetailWebHandler(Long id, LogManager logManager) {
        String transactionId = "ONE_PLATFORM_BA_ONBOARDING_" + UUID.randomUUID() + System.currentTimeMillis();

        Log log = new Log(transactionId, "info", "Fetching onboarded agents on web", "", "", "", "ONE_PLATFORM_BA_ONBOARDING", "ONE_PLATFORM_BA_ONBOARDING",
                "0", "Default!", "Default Detailed Message.", "");

        try {
            String initiatorEmail = helper.extractEmailFromRequest(logManager, log, transactionId);

            ResponseTemplate<UserDetailsResponseDto> userDetailsResponse = commonService.getUserBasicDetails(null, initiatorEmail, internalUsersEntityRepository, externalUsersEntityRepository, logManager, log);
            if (userDetailsResponse.isSuccess()) {
                if (Helper.hasAccess(Authorization.CAN_VIEW_ALL_STATUS_USERS_WEB, userDetailsResponse.getData().getUserHierarchies())) {
                    Optional<BAOnboardingDetails> baOnboardingDetailsOptional = baOnboardingDetailsRepository.findById(id);
                    if (baOnboardingDetailsOptional.isPresent()) {
                        return ResponseTemplate.success(agentDetailsWebResponseBuilder(baOnboardingDetailsOptional.get()));
                    }
                    else {
                        logManager.info(loggerCommon.getLog(log, "info", transactionId, 1, "Agent not found!", "User by the specified id [ " + id + " ], requested by [ " + initiatorEmail + " ], is not found!"));
                        return ResponseTemplate.error("Agent not found!");
                    }
                }
                else {
                    logManager.info(loggerCommon.getLog(log, "info",transactionId,  1, "Unauthorized!", "User by the specified username [ " + initiatorEmail + " ] with the roles [ " + userDetailsResponse.getData().getUserHierarchies() + " ] is not authorized!"));
                    return ResponseTemplate.error("Unauthorized!");
                }
            } else {
                return userDetailsResponse;
            }
        } catch (Exception e) {
            e.printStackTrace();
            logManager.error(loggerCommon.getLog(log, transactionId,  1, "Something went wrong! Please try again.", e));
            return ResponseTemplate.error("Something went wrong! Please try again.");
        }
    }

    private Object agentDetailsAppResponseBuilder(BAOnboardingDetails baOnboardingDetails) {
        AgentDetailsAppResponseDto responseDto = AgentDetailsAppResponseDto.builder()
                .id(baOnboardingDetails.getId())
                .agentId(baOnboardingDetails.getAgentId())
                .nidFullName(Helper.formatFullName(baOnboardingDetails.getNidFirstName(), baOnboardingDetails.getNidMiddleName(), baOnboardingDetails.getNidLastName()))
                .status(baOnboardingDetails.getStatus())
                .userStatus(baOnboardingDetails.getUserStatus())
                .faydaNumber(baOnboardingDetails.getFaydaNumber())
                .msisdn(baOnboardingDetails.getMsisdn())
                .distributorId(baOnboardingDetails.getDistributorId())
                .distributor("N/A")
                .distributorShopId(baOnboardingDetails.getDistributorShopId())
                .distributorShop("N/A")
                .latitude(baOnboardingDetails.getLatitude())
                .longitude(baOnboardingDetails.getLongitude())
                .region(baOnboardingDetails.getRegion())
                .cluster(baOnboardingDetails.getCluster())
                .createdOn(baOnboardingDetails.getCreatedOn())
                .rsm(baOnboardingDetails.getRsm())
                .isNew(!baOnboardingDetails.getIsReverified())
                .address(baOnboardingDetails.getNidAddress() != null ? new AgentDetailsWebResponseDto.Address(
                        baOnboardingDetails.getNidAddress().getKebele(),
                        baOnboardingDetails.getNidAddress().getNidRegion(),
                        baOnboardingDetails.getNidAddress().getWoreda(),
                        baOnboardingDetails.getNidAddress().getZone()) : AgentDetailsWebResponseDto.Address.builder()
                        .zone("N/A")
                        .kebele("N/A")
                        .woreda("N/A")
                        .nidRegion("N/A")
                        .build()
                )
                .build();

        if (baOnboardingDetails.getRsm() != null && !baOnboardingDetails.getRsm().isBlank()) {
            ResponseTemplate<UserDetailsResponseDto> rsmResponse = commonService.getUserBasicDetails(null, baOnboardingDetails.getRsm(), internalUsersEntityRepository, externalUsersEntityRepository, new LogManager(), new Log());
            if (rsmResponse.isSuccess()) {
                responseDto.setRsm(Helper.formatFullName(rsmResponse.getData().getFirstName(), rsmResponse.getData().getMiddleName(), rsmResponse.getData().getLastName()));
            }
        }

//        if (baOnboardingDetails.getReverifiedBy() != null && !baOnboardingDetails.getReverifiedBy().isBlank()) {
//            ResponseTemplate<UserDetailsResponseDto> reverifiedByResponse = commonService.getUserBasicDetails(baOnboardingDetails.getReverifiedById(), baOnboardingDetails.getReverifiedBy(), internalUsersEntityRepository, externalUsersEntityRepository, new LogManager(), new Log()
//            );
//
//            if (reverifiedByResponse.isSuccess()) {
//                responseDto.setReverifiedBy(
//                        Helper.formatFullName(
//                                reverifiedByResponse.getData().getFirstName(),
//                                reverifiedByResponse.getData().getMiddleName(),
//                                reverifiedByResponse.getData().getLastName()
//                        )
//                );
//            }
//        }

        if (baOnboardingDetails.getDistributorId() != null && baOnboardingDetails.getDistributorId() > 0L) {
            channelsEntityRepository.findById(baOnboardingDetails.getDistributorId()).map(ChannelsEntity::getChannelName).ifPresent(responseDto::setDistributor);
        }

        if (baOnboardingDetails.getDistributorShopId() != null && baOnboardingDetails.getDistributorShopId() > 0L) {
            channelsEntityRepository.findById(baOnboardingDetails.getDistributorShopId()).map(ChannelsEntity::getChannelName).ifPresent(responseDto::setDistributorShop);
        }

        return responseDto;
    }

    private AgentDetailsWebResponseDto agentDetailsWebResponseBuilder(BAOnboardingDetails baOnboardingDetails) {
        AgentDetailsWebResponseDto responseDto = AgentDetailsWebResponseDto.builder()
                .id(baOnboardingDetails.getId())
                .agentId(baOnboardingDetails.getAgentId())
                .crmFullName(Helper.formatFullName(baOnboardingDetails.getCrmFirstName(), baOnboardingDetails.getCrmMiddleName(), baOnboardingDetails.getCrmLastName()))
                .nidFirstName(baOnboardingDetails.getNidFirstName())
                .nidMiddleName(baOnboardingDetails.getNidMiddleName())
                .nidLastName(baOnboardingDetails.getNidLastName())
                .nidDateOfBirth(baOnboardingDetails.getNidDateOfBirth())
                .status(baOnboardingDetails.getStatus())
                .reasonForApplicationStatusUpdate(baOnboardingDetails.getReasonForApplicationStatusUpdate())
                .userStatus(baOnboardingDetails.getUserStatus())
                .reasonForUserUpdate(baOnboardingDetails.getReasonForUserUpdate())
                .faydaNumber(baOnboardingDetails.getFaydaNumber())
                .msisdn(baOnboardingDetails.getMsisdn())
                .distributorId(baOnboardingDetails.getDistributorId())
                .distributor("N/A")
                .distributorShopId(baOnboardingDetails.getDistributorShopId())
                .distributorShop("N/A")
                .latitude(baOnboardingDetails.getLatitude())
                .longitude(baOnboardingDetails.getLongitude())
                .division(baOnboardingDetails.getDivision())
                .region(baOnboardingDetails.getRegion())
                .cluster(baOnboardingDetails.getCluster())
                .route(baOnboardingDetails.getRoute())
                .site(baOnboardingDetails.getSite())
                .approvedBy(baOnboardingDetails.getApprovedBy())
                .approvedOn(baOnboardingDetails.getApprovedOn())
                .createdBy(baOnboardingDetails.getCreatedBy())
             //   .reverifiedBy(baOnboardingDetails.getReverifiedBy())
                .createdOn(baOnboardingDetails.getCreatedOn())
                .updatedBy(baOnboardingDetails.getUpdatedBy())
                .updatedOn(baOnboardingDetails.getUpdatedOn() != null ? baOnboardingDetails.getUpdatedOn().toString() : null)
                .isAgentCreatedOnEkyc(baOnboardingDetails.getIsAgentCreatedOnEkyc())
                .isAgentCreatedOnDxl(baOnboardingDetails.getIsAgentCreatedOnDxl())
                .isAgentCreatedOnBD(baOnboardingDetails.getIsAgentCreatedOnBD())
                .letterOfConsentList(reverseList(baOnboardingDetails.getLetterOfConsentList()))
                .agentPhotoList(reverseList(baOnboardingDetails.getAgentPhotoList()))
                .nidCardPictureList(baOnboardingDetails.getNidCardPictureList().stream()
                        .map(nid -> AgentDetailsWebResponseDto.NIDCardPicture.builder()
                                .nidPictureType(nid.getNidPictureType())
                                .nidPicturePath(nid.getNidPicturePath())
                                .build())
                        .collect(Collectors.toList()))
                .nidIDExpirationDate(baOnboardingDetails.getNidIDExpirationDate())
                .nidPhotoPath(baOnboardingDetails.getNidPhotoPath())
                .rsm(baOnboardingDetails.getRsm())
                .ram(baOnboardingDetails.getRam())
                .address(baOnboardingDetails.getNidAddress() != null
                        ? new AgentDetailsWebResponseDto.Address(
                        baOnboardingDetails.getNidAddress().getKebele(),
                        baOnboardingDetails.getNidAddress().getNidRegion(),
                        baOnboardingDetails.getNidAddress().getWoreda(),
                        baOnboardingDetails.getNidAddress().getZone())
                        : AgentDetailsWebResponseDto.Address.builder()
                        .kebele("N/A").nidRegion("N/A").woreda("N/A").zone("N/A")
                        .build()
                )
                .build();

        Collections.reverse(responseDto.getNidCardPictureList());

        if (baOnboardingDetails.getCreatedBy() != null && !baOnboardingDetails.getCreatedBy().isBlank()) {
            ResponseTemplate<UserDetailsResponseDto> createdByResponse = commonService.getUserBasicDetails(baOnboardingDetails.getCreatedById(), baOnboardingDetails.getCreatedBy(), internalUsersEntityRepository, externalUsersEntityRepository, new LogManager(), new Log());
            if (createdByResponse.isSuccess()) {
                responseDto.setCreatedBy(Helper.formatFullName(createdByResponse.getData().getFirstName(), createdByResponse.getData().getMiddleName(), createdByResponse.getData().getLastName()));
            }
            else {
                createdByResponse = commonService.getUserBasicDetails(baOnboardingDetails.getCreatedById(), baOnboardingDetails.getCreatedBy(), internalUsersEntityRepository, externalUsersEntityRepository, new LogManager(), new Log());
                if (createdByResponse.isSuccess()) {
                    responseDto.setCreatedBy(Helper.formatFullName(createdByResponse.getData().getFirstName(), createdByResponse.getData().getMiddleName(), createdByResponse.getData().getLastName()));
                }
            }
        }

//        if (baOnboardingDetails.getReverifiedBy() != null && !baOnboardingDetails.getReverifiedBy().isBlank()) {
//            ResponseTemplate<UserDetailsResponseDto> reverifiedByResponse = commonService.getUserBasicDetails(baOnboardingDetails.getReverifiedById(), baOnboardingDetails.getReverifiedBy(), internalUsersEntityRepository, externalUsersEntityRepository, new LogManager(), new Log()
//                    );
//
//            if (reverifiedByResponse.isSuccess()) {
//                responseDto.setReverifiedBy(
//                        Helper.formatFullName(
//                                reverifiedByResponse.getData().getFirstName(),
//                                reverifiedByResponse.getData().getMiddleName(),
//                                reverifiedByResponse.getData().getLastName()
//                        )
//                );
//            }
//        }
//


        if (baOnboardingDetails.getUpdatedBy() != null && !baOnboardingDetails.getUpdatedBy().isBlank()) {
            ResponseTemplate<UserDetailsResponseDto> updatedByResponse = commonService.getUserBasicDetails(baOnboardingDetails.getUpdatedById(), baOnboardingDetails.getUpdatedBy(), internalUsersEntityRepository, externalUsersEntityRepository, new LogManager(), new Log());
            if (updatedByResponse.isSuccess()) {
                responseDto.setUpdatedBy(Helper.formatFullName(updatedByResponse.getData().getFirstName(), updatedByResponse.getData().getMiddleName(), updatedByResponse.getData().getLastName()));
            }
            else {
                updatedByResponse = commonService.getUserBasicDetails(baOnboardingDetails.getUpdatedById(), baOnboardingDetails.getUpdatedBy(), internalUsersEntityRepository, externalUsersEntityRepository, new LogManager(), new Log());
                if (updatedByResponse.isSuccess()) {
                    responseDto.setUpdatedBy(Helper.formatFullName(updatedByResponse.getData().getFirstName(), updatedByResponse.getData().getMiddleName(), updatedByResponse.getData().getLastName()));
                }
            }
        }

        if (baOnboardingDetails.getApprovedBy() != null && !baOnboardingDetails.getApprovedBy().isBlank()) {
            ResponseTemplate<UserDetailsResponseDto> approvedByResponse = commonService.getUserBasicDetails(baOnboardingDetails.getApprovedById(), baOnboardingDetails.getApprovedBy(), internalUsersEntityRepository, externalUsersEntityRepository, new LogManager(), new Log());
            if (approvedByResponse.isSuccess()) {
                responseDto.setApprovedBy(Helper.formatFullName(approvedByResponse.getData().getFirstName(), approvedByResponse.getData().getMiddleName(), approvedByResponse.getData().getLastName()));
            }
        }

        if (baOnboardingDetails.getRsm() != null && !baOnboardingDetails.getRsm().isBlank()) {
            ResponseTemplate<UserDetailsResponseDto> rsmResponse = commonService.getUserBasicDetails(null, baOnboardingDetails.getRsm(), internalUsersEntityRepository, externalUsersEntityRepository, new LogManager(), new Log());
            if (rsmResponse.isSuccess()) {
                responseDto.setRsm(Helper.formatFullName(rsmResponse.getData().getFirstName(), rsmResponse.getData().getMiddleName(), rsmResponse.getData().getLastName()));
            }
        }

        if (baOnboardingDetails.getRam() != null && !baOnboardingDetails.getRam().isBlank()) {
            ResponseTemplate<UserDetailsResponseDto> ramResponse = commonService.getUserBasicDetails(null, baOnboardingDetails.getRam(), internalUsersEntityRepository, externalUsersEntityRepository, new LogManager(), new Log());
            if (ramResponse.isSuccess()) {
                responseDto.setRam(Helper.formatFullName(ramResponse.getData().getFirstName(), ramResponse.getData().getMiddleName(), ramResponse.getData().getLastName()));
            }
        }

        if (baOnboardingDetails.getDistributorId() != null && baOnboardingDetails.getDistributorId() > 0L) {
            channelsEntityRepository.findById(baOnboardingDetails.getDistributorId()).map(ChannelsEntity::getChannelName).ifPresent(responseDto::setDistributor);
        }

        if (baOnboardingDetails.getDistributorShopId() != null && baOnboardingDetails.getDistributorShopId() > 0L) {
            channelsEntityRepository.findById(baOnboardingDetails.getDistributorShopId()).map(ChannelsEntity::getChannelName).ifPresent(responseDto::setDistributorShop);
        }

        return responseDto;
    }

    public ResponseTemplate<?> userRequiredFields(Long id, LogManager logManager) {
        String transactionId = "ONE_PLATFORM_BA_ONBOARDING_" + UUID.randomUUID()+  System.currentTimeMillis();

        Log log = new Log(transactionId, "info" , "Fetching required fields for the app", "", "", "", "ONE_PLATFORM_BA_ONBOARDING", "ONE_PLATFORM_BA_ONBOARDING",
                "0", "Default!", "Default Detailed Message.", "");

        try {
            String initiatorEmail = helper.extractEmailFromRequest(logManager, log , transactionId);

            ResponseTemplate<UserDetailsResponseDto> userDetailsResponse = commonService.getUserBasicDetails(null, initiatorEmail, internalUsersEntityRepository, externalUsersEntityRepository, logManager, log);
            if (userDetailsResponse.isSuccess()) {
                if (Helper.hasAccess(Authorization.CAN_ONBOARD_APP, userDetailsResponse.getData().getUserHierarchies())) {
                    Optional<BAOnboardingDetails> baOnboardingDetailsOptional = baOnboardingDetailsRepository.findById(id);
                    if (baOnboardingDetailsOptional.isPresent()) {
                        BAOnboardingDetails baOnboardingDetails = baOnboardingDetailsOptional.get();
                        if (baOnboardingDetails.getCreatedBy().equals(initiatorEmail)) {
                            String remark = !baOnboardingDetails.getBoRemarkList().isEmpty() ? reverseList(baOnboardingDetails.getBoRemarkList()).get(0) : "No Remark has been given.";
                            List<Map<String, Object>> content = getUpdatedFieldsResponseContentEnhanced(baOnboardingDetails);

                            return ResponseTemplate.success(Map.of(
                                    "id", baOnboardingDetails.getId(),
                                    "remark", remark,
                                    "content", content
                            ));
                        } else {
                            logManager.info(loggerCommon.getLog(log, "info",transactionId,  1, "Unauthorized!", "User [ " + initiatorEmail + " ] is trying to complete the required fields for an agent onboarded by [ " + baOnboardingDetails.getCreatedBy() + " ]"));
                            return ResponseTemplate.error("Unauthorized!");
                        }
                    } else {
                        logManager.info(loggerCommon.getLog(log, "info", transactionId, 1, "Agent not found!", "User by the specified id [ " + id + " ], requested by [ " + initiatorEmail + " ], is not found!"));
                        return ResponseTemplate.error("Agent not found!");
                    }
                } else {
                    logManager.info(loggerCommon.getLog(log, "info", transactionId,1, "Unauthorized!", "User by the specified username [ " + initiatorEmail + " ] with the roles [ " + userDetailsResponse.getData().getUserHierarchies() + " ] is not authorized!"));
                    return ResponseTemplate.error("Unauthorized!");
                }
            } else {
                return userDetailsResponse;
            }
        } catch (Exception e) {
            e.printStackTrace();
            logManager.error(loggerCommon.getLog(log,transactionId, 1, "Something went wrong! Please try again.", e));
            return ResponseTemplate.error("Something went wrong! Please try again.");
        }
    }

    private List<Map<String, Object>> getUpdatedFieldsResponseContent(BAOnboardingDetails baOnboardingDetails) {
        Optional<BOUpdatableFields> latestUpdate = baOnboardingDetails.getBoUpdatableFieldList().stream()
                .max(Comparator.comparing(BOUpdatableFields::getRequestedOn));

        if (latestUpdate.isEmpty()) {
            return List.of();
        }

        String[] updatedFields = latestUpdate.get().getUpdatableFields().split(",");

        Map<BA_ONBOARDING_APP_PAGES, List<String>> fieldsByPage = Arrays.stream(updatedFields)
                .map(String::trim)
                .map(field -> BA_ONBOARDING_APP_PAGES.getPageByField(field).map(page -> new AbstractMap.SimpleEntry<>(page, field)))
                .flatMap(Optional::stream)
                .collect(Collectors.groupingBy(
                        Map.Entry::getKey,
                        Collectors.mapping(Map.Entry::getValue, Collectors.toList())
                ));

        List<Map<String, Object>> responseContent = new ArrayList<>();

        for (Map.Entry<BA_ONBOARDING_APP_PAGES, List<String>> entry : fieldsByPage.entrySet()) {
            BA_ONBOARDING_APP_PAGES page = entry.getKey();
            List<String> fields = entry.getValue();
            if (page == null) continue;
            Map<String, Object> pageData = new HashMap<>();
            for (String field : fields) {
                Object value = Helper.getFieldValue(baOnboardingDetails, field);
                pageData.put(field, value);
            }

            Map<String, Object> pageResponse = new HashMap<>();
            pageResponse.put("sessionId", baOnboardingDetails.getSessionId());
            pageResponse.put("pageId", page.name());
            pageResponse.put("data", pageData);

            responseContent.add(pageResponse);
        }

        return responseContent;
    }

    private List<Map<String, Object>> getUpdatedFieldsResponseContentEnhanced(BAOnboardingDetails baOnboardingDetails) {
        Optional<BOUpdatableFields> latestUpdate = baOnboardingDetails.getBoUpdatableFieldList().stream()
                .max(Comparator.comparing(BOUpdatableFields::getRequestedOn));

        Map<BA_ONBOARDING_APP_PAGES, Map<String, Object>> allFieldsByPage = new HashMap<>();
        Map<BA_ONBOARDING_APP_PAGES, Map<String, Object>> updatedFieldsByPage = new HashMap<>();
        Set<String> updatedFieldsSet = new HashSet<>();

        if (latestUpdate.isPresent()) {
            String[] updatedFields = latestUpdate.get().getUpdatableFields().split(",");
            updatedFieldsSet.addAll(Arrays.asList(updatedFields));
        }

        for (String field : getAllFields(baOnboardingDetails)) {
            String fieldName = field;
            if (field.equals("rsm")) fieldName = "rsmUsername";
            if (field.equals("ram")) fieldName = "ramUsername";
            if (field.equals("nidFirstName") || field.equals("nidMiddleName") || field.equals("nidLastName"))
                fieldName = "nidFullName";
            BA_ONBOARDING_APP_PAGES page = BA_ONBOARDING_APP_PAGES.getPageByField(fieldName).orElse(null);
            if (page == null) continue;

            Object fieldValue = Helper.getFieldValue(baOnboardingDetails, field);
            if (updatedFieldsSet.contains(field)) {
                updatedFieldsByPage.computeIfAbsent(page, k -> new HashMap<>()).put(field, fieldValue);
            } else {
                allFieldsByPage.computeIfAbsent(page, k -> new HashMap<>()).put(field, fieldValue);
            }
        }

        BA_ONBOARDING_APP_PAGES personalInformationPage = BA_ONBOARDING_APP_PAGES.PERSONAL_INFORMATION_PAGE;
        Map<String, Object> fields = allFieldsByPage.get(personalInformationPage);
        if (fields != null) {
            String firstName = (String) fields.get("nidFirstName");
            String middleName = (String) fields.get("nidMiddleName");
            String lastName = (String) fields.get("nidLastName");
            String nidFullName = "N/A";
            if (firstName != null || middleName != null || lastName != null) {
                nidFullName = Helper.formatFullName(firstName, middleName, lastName);
                fields.remove("nidFirstName");
                fields.remove("nidMiddleName");
                fields.remove("nidLastName");
            }
            fields.put("nidFullName", nidFullName);
            allFieldsByPage.put(personalInformationPage, fields);
        }

        List<Map<String, Object>> responseContent = new ArrayList<>();
        for (BA_ONBOARDING_APP_PAGES page : updatedFieldsByPage.keySet()) {
            Map<String, Object> response = new HashMap<>();
            response.put("sessionId", baOnboardingDetails.getSessionId());
            response.put("pageId", page.name());
            response.put("data", updatedFieldsByPage.getOrDefault(page, new HashMap<>()));
            Map<String, Object> details = new HashMap<>(allFieldsByPage.getOrDefault(page, new HashMap<>()));
            details.keySet().removeAll(updatedFieldsByPage.getOrDefault(page, new HashMap<>()).keySet());
            response.put("details", details);
            responseContent.add(response);
        }
        return responseContent;
    }

    private List<String> getAllFields(BAOnboardingDetails baOnboardingDetails) {
        return Arrays.stream(baOnboardingDetails.getClass().getDeclaredFields())
                .map(Field::getName)
                .collect(Collectors.toList());
    }

    public ResponseTemplate<?> getStatisticsRouter(STATISTICS_TYPE type, LogManager logManager) {
        String transactionId = "ONE_PLATFORM_BA_ONBOARDING_" + System.currentTimeMillis();

        Log log = new Log(transactionId, "info", "Fetching agents statistics on web", "Fetching agents statistics on web", "", "", "ONE_PLATFORM_BA_ONBOARDING", "ONE_PLATFORM_BA_ONBOARDING",
                "0", "Default!", "Default Detailed Message.", "");

        try {
            String initiatorEmail = helper.extractEmailFromRequest(logManager, log, transactionId);

            ResponseTemplate<UserDetailsResponseDto> userDetailsResponse = commonService.getUserBasicDetails(null, initiatorEmail, internalUsersEntityRepository, externalUsersEntityRepository, logManager, log);
            if (userDetailsResponse.isSuccess()) {
                if (Helper.hasAccess(Authorization.CAN_VIEW_REPORT_AND_DASHBOARD_WEB, userDetailsResponse.getData().getUserHierarchies())) {
                    switch (type) {
                        case TOTAL_AGENTS_CREATED -> {
                            return getTotalAgentsCreated(initiatorEmail, logManager, log);
                        }
                        case ACTIVE_VS_INACTIVE_AGENTS -> {
                            return getActiveVSInactiveAgents(initiatorEmail, userDetailsResponse.getData(), logManager, log);
                        }
                        case APPROVAL_RATE -> {
                            return getApprovalRate(initiatorEmail, userDetailsResponse.getData(), logManager, log);
                        }
                        case GEOGRAPHICAL_DISTRIBUTION -> {
                            return getByGeographicalDistribution(initiatorEmail, logManager, log);
                        }
                        case LAST_30_DAYS_USERS_CREATED -> {
                            return getLast30DaysUsersCreated(initiatorEmail, logManager, log);
                        }
                        default -> { // All / Bad Request
                            return ResponseTemplate.error("Bad Request! You must specify the type of the statistics.");
                        }
                    }
                } else {
                    logManager.info(loggerCommon.getLog(log, "info",transactionId, 1, "Unauthorized!", "User by the specified username [ " + initiatorEmail + " ] with the roles [ " + userDetailsResponse.getData().getUserHierarchies() + " ] is not authorized!"));
                    return ResponseTemplate.error("Unauthorized!");
                }
            } else {
                return userDetailsResponse;
            }
        } catch (Exception e) {
            e.printStackTrace();
            logManager.error(loggerCommon.getLog(log,transactionId,  1, "Something went wrong! Please try again.", e));
            return ResponseTemplate.error("Something went wrong! Please try again.");
        }
    }

    private ResponseTemplate<?> getTotalAgentsCreated(String initiatorEmail, LogManager logManager, Log log) {
        log.setProcess("Fetching Total Agents Created");

        Integer totalAgents = baOnboardingDetailsRepository.countAllByStatus(APPLICATION_STATUS.APPROVED);
        if (totalAgents == null) {
            logManager.info(loggerCommon.getLog(log, "info", log.getTransactionID(), 1, "There are no agents created currently.", "[ " + initiatorEmail + " ] Total agents successfully created == null"));
            return ResponseTemplate.error("There are no agents created currently.");
        }
        logManager.info(loggerCommon.getLog(log, "info", log.getTransactionID(), 0, "Success!", "[ " + initiatorEmail + " ] Total agents successfully created == " + totalAgents));
        return ResponseTemplate.success(totalAgents);
    }

    private ResponseTemplate<?> getActiveVSInactiveAgents(String initiatorEmail, UserDetailsResponseDto userDetailsResponse, LogManager logManager, Log log) {
        log.setProcess("Fetching Active vs Inactive Agents");

        List<Integer> totalIntegerList = getTotalNumberOfAgentsByStatus(initiatorEmail, userDetailsResponse, new ArrayList<>());

        Map<String, Object> response = new HashMap<>();
        response.put("totalActiveAgents", totalIntegerList.get(0));
        response.put("totalRejectedAgents", totalIntegerList.get(1));
        response.put("totalPendingAgents", totalIntegerList.get(2));
        response.put("totalCreatedAgents", totalIntegerList.get(0) + totalIntegerList.get(1) + totalIntegerList.get(2));

        logManager.info(loggerCommon.getLog(log, "info", log.getTransactionID(), 0, "Success!", "[ " + initiatorEmail + " ] Response: " + response));
        return ResponseTemplate.success(response);
    }

    private ResponseTemplate<?> getApprovalRate(String initiatorEmail, UserDetailsResponseDto userDetailsResponse, LogManager logManager, Log log) {
        log.setProcess("Fetching Approval Rate");
        List<Integer> totalIntegerList = getTotalNumberOfAgentsByStatus(initiatorEmail, userDetailsResponse, new ArrayList<>());
        int totalAgents = totalIntegerList.get(0) + totalIntegerList.get(1) + totalIntegerList.get(2);

        double activeAgentsRate = (double) totalIntegerList.get(0) / totalAgents * 100;
        double rejectedAgentsRate = (double) totalIntegerList.get(1) / totalAgents * 100;
        double pendingAgentsRate = (double) totalIntegerList.get(2) / totalAgents * 100;

        Map<String, Object> response = new HashMap<>();
        response.put("activeAgentsApprovalRate", Helper.round(activeAgentsRate, 2));
        response.put("rejectedAgentsApprovalRate", Helper.round(rejectedAgentsRate, 2));
        response.put("pendingAgentsApprovalRate", Helper.round(pendingAgentsRate, 2));

        logManager.info(loggerCommon.getLog(log, "info", log.getTransactionID(), 0, "Success!", "[ " + initiatorEmail + " ] Response: " + response));
        return ResponseTemplate.success(response);
    }

    private List<Integer> getTotalNumberOfAgentsByStatus(String initiatorEmail, UserDetailsResponseDto userDetailsResponse, List<String> rams) {
        Integer totalActiveAgents = 0;
        Integer totalRejectedAgents = 0;
        Integer totalPendingAgents = 0;
        Integer totalCreatedAgents = 0;
        if (initiatorEmail != null && !initiatorEmail.isBlank()) {
            if (userDetailsResponse.getUserHierarchies().contains(Constants.DISTRIBUTION_RSM_ROLE_ID)) {
//                if (!rams.isEmpty()) {}
                System.err.println("|RSM - " + initiatorEmail);
                totalActiveAgents = baOnboardingDetailsRepository.countAllByStatusAndUser(APPLICATION_STATUS.APPROVED, initiatorEmail, "");
                totalRejectedAgents = baOnboardingDetailsRepository.countAllByStatusAndUser(APPLICATION_STATUS.REJECTED, initiatorEmail, "");
                totalPendingAgents = baOnboardingDetailsRepository.countAllByStatusAndUser(APPLICATION_STATUS.PENDING, initiatorEmail, "");
                totalCreatedAgents = totalActiveAgents + totalRejectedAgents + totalPendingAgents;
            } else if (userDetailsResponse.getUserHierarchies().contains(Constants.RAM_ROLE_ID)) {
                totalActiveAgents = baOnboardingDetailsRepository.countAllByStatusAndUser(APPLICATION_STATUS.APPROVED, "", initiatorEmail);
                totalRejectedAgents = baOnboardingDetailsRepository.countAllByStatusAndUser(APPLICATION_STATUS.REJECTED, "", initiatorEmail);
                totalPendingAgents = baOnboardingDetailsRepository.countAllByStatusAndUser(APPLICATION_STATUS.PENDING, "", initiatorEmail);
                totalCreatedAgents = totalActiveAgents + totalRejectedAgents + totalPendingAgents;
            }
            else if (userDetailsResponse.getUserHierarchies().contains(Constants.DISTRIBUTION_ADMIN_ID)) {
                totalActiveAgents = baOnboardingDetailsRepository.countAllByStatusAndDistributor(APPLICATION_STATUS.APPROVED, userDetailsResponse.getChannelId(), 0L);
                totalRejectedAgents = baOnboardingDetailsRepository.countAllByStatusAndDistributor(APPLICATION_STATUS.REJECTED, userDetailsResponse.getChannelId(), 0L);
                totalPendingAgents = baOnboardingDetailsRepository.countAllByStatusAndDistributor(APPLICATION_STATUS.PENDING, userDetailsResponse.getChannelId(), 0L);
                totalCreatedAgents = totalActiveAgents + totalRejectedAgents + totalPendingAgents;
            }
            else if (userDetailsResponse.getUserHierarchies().contains(Constants.DISTRIBUTION_SHOP_MANAGER_ID)) {
                totalActiveAgents = baOnboardingDetailsRepository.countAllByStatusAndDistributor(APPLICATION_STATUS.APPROVED, 0L, userDetailsResponse.getChannelId());
                totalRejectedAgents = baOnboardingDetailsRepository.countAllByStatusAndDistributor(APPLICATION_STATUS.REJECTED, 0L, userDetailsResponse.getChannelId());
                totalPendingAgents = baOnboardingDetailsRepository.countAllByStatusAndDistributor(APPLICATION_STATUS.PENDING, 0L, userDetailsResponse.getChannelId());
                totalCreatedAgents = totalActiveAgents + totalRejectedAgents + totalPendingAgents;
            }
            else {
                totalActiveAgents = baOnboardingDetailsRepository.countAllByStatus(APPLICATION_STATUS.APPROVED);
                totalRejectedAgents = baOnboardingDetailsRepository.countAllByStatus(APPLICATION_STATUS.REJECTED);
                totalPendingAgents = baOnboardingDetailsRepository.countAllByStatus(APPLICATION_STATUS.PENDING);
                totalCreatedAgents = baOnboardingDetailsRepository.countAllByStatusInverseWithException(APPLICATION_STATUS.APPROVED, APPLICATION_STATUS.DRAFT);
            }
        }

        totalActiveAgents = totalActiveAgents != null ? totalActiveAgents : 0;
        totalRejectedAgents = totalRejectedAgents != null ? totalRejectedAgents : 0;
        totalPendingAgents = totalPendingAgents != null ? totalPendingAgents : 0;
        totalCreatedAgents = totalCreatedAgents != null ? totalCreatedAgents : 0;

        return List.of(totalActiveAgents, totalRejectedAgents, totalPendingAgents, totalCreatedAgents);
    }

    private ResponseTemplate<?> getByGeographicalDistribution(String initiatorEmail, LogManager logManager, Log log) {
        log.setProcess("Fetching By Geographical Distribution");

        Map<String, Integer> divisionCounts = Helper.processGeoLocationResults(baOnboardingDetailsRepository.countAllByDivision(APPLICATION_STATUS.APPROVED));
        Map<String, Integer> regionCounts = Helper.processGeoLocationResults(baOnboardingDetailsRepository.countAllByRegion(APPLICATION_STATUS.APPROVED));
        Map<String, Integer> clusterCounts = Helper.processGeoLocationResults(baOnboardingDetailsRepository.countAllByCluster(APPLICATION_STATUS.APPROVED));

        CountByLocationQueryResponseDto response = CountByLocationQueryResponseDto.builder().divisionCounts(divisionCounts).regionCounts(regionCounts).clusterCounts(clusterCounts).build();
        logManager.info(loggerCommon.getLog(log, "info", log.getTransactionID(), 0, "Success!", "[ " + initiatorEmail + " ] Response: " + response));
        return ResponseTemplate.success(response);
    }


    private ResponseTemplate<?> getLast30DaysUsersCreated(String initiatorEmail, LogManager logManager, Log log) {
        log.setProcess("Fetching The Last 30 Days of User Creations");

        Integer totalCreatedAgentsOfLast30Days = baOnboardingDetailsRepository.countAllByStatusInverseWithExceptionAndLast30Days(APPLICATION_STATUS.APPROVED, APPLICATION_STATUS.DRAFT, LocalDateTime.now().minusDays(30L), LocalDateTime.now());
        totalCreatedAgentsOfLast30Days = totalCreatedAgentsOfLast30Days != null ? totalCreatedAgentsOfLast30Days : 0;

        Map<String, Object> response = new LinkedHashMap<>();
        response.put("totalCreatedAgentsOfLast30Days", totalCreatedAgentsOfLast30Days);

        logManager.info(loggerCommon.getLog(log, "info", log.getTransactionID(), 0, "Success!", "[ " + initiatorEmail + " ] Response: " + response));
        return ResponseTemplate.success(response);
    }

    private ResponseTemplate<BAOnboardingDetails> ekycLastResort(BAOnboardingDetails updateBAOnboardingDetails, LogManager logManager, Log log , String transactionId) {
        ResponseTemplate<List<String>> eKycDeDupHandlerResponse = apiGatewayService.eKycDeDupHandler(new EKycVerificationApiRequestDto(), updateBAOnboardingDetails.getCrmBiometricsId(), EKYC_DEDUB_TYPE.BY_BIOMETRICS_ID, logManager, log);
        if (eKycDeDupHandlerResponse.isSuccess()) {
            List<BAOnboardingDetails> findAllByBiometricsIdList = baOnboardingDetailsRepository.findAllByBiometricsIdList(eKycDeDupHandlerResponse.getData());
            if (findAllByBiometricsIdList.isEmpty() || findAllByBiometricsIdList.stream().filter(ba -> !ba.getStatus().equals(APPLICATION_STATUS.DRAFT)).toList().isEmpty()) {
                ResponseTemplate<?> eKycVettingResponse = apiGatewayService.eKycVetting(updateBAOnboardingDetails.getCrmBiometricsId(), logManager, log);
                if (eKycVettingResponse.isSuccess()) {
                    if (eKycDeDupHandlerResponse.getData() != null) {
                        if (updateBAOnboardingDetails.getBiometricsIdList() == null) {
                            updateBAOnboardingDetails.setBiometricsIdList(new ArrayList<>());
                        }
                        updateBAOnboardingDetails.getBiometricsIdList().addAll(eKycDeDupHandlerResponse.getData());
                    }
                    return ResponseTemplate.success(updateBAOnboardingDetails);
                }
                else {
                    logManager.info(loggerCommon.getLog(log, "info", transactionId, 1, "Vetting failed!", "Vetting failed for this id [ " + updateBAOnboardingDetails.getCrmBiometricsId() + " ]"));
                    return ResponseTemplate.error("Vetting failed!");
                }
            }
            else {
                logManager.info(loggerCommon.getLog(log, "info", transactionId, 1, "User already exists!", "Duplicated biometrics data found!"));
                return ResponseTemplate.error("User already exists!");
            }
        } else {
            return ResponseTemplate.error(eKycDeDupHandlerResponse.getMessage());
        }
    }

    public ResponseTemplate<?> getSnDBAInformation(String agentId, LogManager logManager) {
        String transactionId = "ONE_PLATFORM_BA_ONBOARDING_" +UUID.randomUUID()+ System.currentTimeMillis();

        Log log = new Log(transactionId, "info", "Fetching BA Information from SnD Dump", "", "", "", "ONE_PLATFORM_BA_ONBOARDING", "ONE_PLATFORM_SnD_DUMP",
                "0", "Default!", "Default Detailed Message.", "");

        try {
            String initiatorEmail = helper.extractEmailFromRequest(logManager, log , transactionId);
            ResponseTemplate<UserDetailsResponseDto> userDetailsResponse = commonService.getUserBasicDetails(null, initiatorEmail, internalUsersEntityRepository, externalUsersEntityRepository, logManager, log);
            if (userDetailsResponse.isSuccess()) {
                if (Helper.hasAccess(Authorization.CAN_ONBOARD_APP, userDetailsResponse.getData().getUserHierarchies())) {
                    Optional<BAOnboardingDetails> baOnboardingDetailsOptional = baOnboardingDetailsRepository.findByAgentId(agentId);
                    if (baOnboardingDetailsOptional.isPresent()) {
                        if (baOnboardingDetailsOptional.get().getStatus().equals(APPLICATION_STATUS.APPROVED) || baOnboardingDetailsOptional.get().getStatus().equals(APPLICATION_STATUS.PENDING)) {
                            return ResponseTemplate.error("An agent by the specified id has already been re-verified or in the process!");
                        }
                    }

                    Optional<SalesUserDetail> sndUserDetailOptional = salesUserDetailRepository.findByUserId(agentId);
                    if (sndUserDetailOptional.isPresent()) {
                        Map<String, Object> response = new LinkedHashMap<>();

                        response.put("fullName", Helper.formatFullName(sndUserDetailOptional.get().getFirstName(), "", sndUserDetailOptional.get().getLastName()));
                        response.put("userId", sndUserDetailOptional.get().getUserId());
                      //  response.put("division", sndUserDetailOptional.get().getDivision());
                      //  response.put("region", sndUserDetailOptional.get().getRegion());
                      //  response.put("cluster", sndUserDetailOptional.get().getCluster());
                        response.put("createdBy", sndUserDetailOptional.get().getAddBy());
                        response.put("onboardedDate", sndUserDetailOptional.get().getStartDate());
                        response.put("msisdn", sndUserDetailOptional.get().getUserContactNo());

                        Map<Integer, String> sndUserTypeMap = Map.ofEntries(
                                Map.entry(-1, "Administrator"),
                                Map.entry(1, "Manager"),
                                Map.entry(2, "Distributor"),
                                Map.entry(3, "Distributors Branch Manager"),
                                Map.entry(4, "Retailer"),
                                Map.entry(5, "DSA"),
                                Map.entry(6, "Warehouse Manager"),
                                Map.entry(7, "Sales Area Manager"),
                                Map.entry(8, "Finance Manager"),
                                Map.entry(9, "BO Inventory"),
                                Map.entry(10, "Customer Management"),
                                Map.entry(11, "Logistics Manager"),
                                Map.entry(12, "Customer Care Executive"),
                                Map.entry(13, "Shipping Vendor"),
                                Map.entry(14, "Logistics Partner"),
                                Map.entry(15, "Shop User"),
                                Map.entry(16, "Sales REE"),
                                Map.entry(17, "TDR"),
                                Map.entry(36, "Distributor Staff"),
                                Map.entry(37, "Store supervisor"),
                                Map.entry(38, "Driver"),
                                Map.entry(39, "EKYC Agent"),
                                Map.entry(40, "STEP EKYC Agent"),
                                Map.entry(41, "Manager Retail Experience"),
                                Map.entry(42, "Regional Retail Center Managers"),
                                Map.entry(43, "Retail Operation Manager"),
                                Map.entry(44, "Retail Inv&Fin Specialist"),
                                Map.entry(45, "Retail Center Team Leaders"),
                                Map.entry(46, "Retail Experience Executives"),
                                Map.entry(47, "Retail center stock Controllers"),
                                Map.entry(48, "Distributor Shop Manager"),
                                Map.entry(49, "DSP"),
                                Map.entry(50, "Fraud Analyst"),
                                Map.entry(51, "Fraud Manager"),
                                Map.entry(52, "Internal Auditor"),
                                Map.entry(53, "Agency BA"),
                                Map.entry(54, "Distributor BA"),
                                Map.entry(55, "Agency"),
                                Map.entry(56, "Nominated User")
                        );

                        Double userTypeIdDou = Double.parseDouble(String.valueOf(sndUserDetailOptional.get().getUserTypeId()));
                        response.put("userCategory", sndUserTypeMap.getOrDefault(userTypeIdDou.intValue(), "N/A"));

                        List<SalesChannelMaster> salesChannelMasterList = salesChannelMasterRepository.findAllByUserId(sndUserDetailOptional.get().getUserId());
                        boolean salesChannelMasterFound = !salesChannelMasterList.isEmpty() && salesChannelMasterList.get(0).getParentId() != null;
                        Long parentId = salesChannelMasterFound ? ((Double) salesChannelMasterList.get(0).getParentId()).longValue() : null;
                        List<SalesChannelMaster> parentSalesChannelMasterList = salesChannelMasterFound ? salesChannelMasterRepository.findAllBySalesChannelId(parentId)  : List.of();
                        response.put("parentDistributorName", !parentSalesChannelMasterList.isEmpty() ? parentSalesChannelMasterList.get(0).getSalesChannelName() : "N/A");
                        response.put("areaSalesManager", sndUserDetailOptional.get().getAreaSalesManager());


                        Double userStatusDou = Double.parseDouble(String.valueOf(sndUserDetailOptional.get().getUserStatus()));
                        Optional<SalesUserStatusMaster> salesUserStatusMasterOptional = salesUserStatusMasterRepository.findByStatusId(userStatusDou.intValue());
                        response.put("userStatus", salesUserStatusMasterOptional.isPresent() ? salesUserStatusMasterOptional.get().getStatusDesc() : "N/A");

                        logManager.info(loggerCommon.getLog(log, "info", transactionId,  0, "Success!", "SnD Simple Response: " + response));
                        return ResponseTemplate.success(response);
                    }
                    else {
                        logManager.info(loggerCommon.getLog(log, "info", transactionId, 1, "An agent by the specified id not found!", "An Agent by the specified id [ " + agentId + " ] is not found!"));
                        return ResponseTemplate.error("An agent by the specified id not found!");
                    }
                }
                else {
                    logManager.info(loggerCommon.getLog(log, "info", transactionId,  1, "Unauthorized!", "User by the specified username [ " + initiatorEmail + " ] with the roles [ " + userDetailsResponse.getData().getUserHierarchies() + " ] is not authorized!"));
                    return ResponseTemplate.error("Unauthorized!");
                }
            }
            else {
                return userDetailsResponse;
            }

//            return ResponseTemplate.success("Hi " + initiatorEmail + ", I'm not yet ready to process your request for [ " + agentId + " ] agent-id.");
        } catch (Exception e) {
            e.printStackTrace();
            logManager.error(loggerCommon.getLog(log, transactionId,  1, "Something went wrong! Please try again.", e));
            return ResponseTemplate.error("Something went wrong! Please try again.");
        }
    }

    public Object userDetailByAgentId(String userAgent, AgentInfoRequestDto agentInfoRequestDto, LogManager logManager) {
        String transactionId = "ONE_PLATFORM_BA_ONBOARDING_" + UUID.randomUUID()+ System.currentTimeMillis();
        Log log = new Log(transactionId, "info", "Fetching BA Information by agent-id", "", "", "", "ONE_PLATFORM_BA_ONBOARDING", "ONE_PLATFORM_BA_ONBOARDING",
                "0", "Default!", "Default Detailed Message.", "");
        try {
            List<BAOnboardingDetails> baOnboardingDetailsList = baOnboardingDetailsRepository.findAllByAgentId(agentInfoRequestDto.getUserId());
            logManager.info(loggerCommon.getLog(log, "info", transactionId, 0, "BA details fetched successfully", "AgentId=" + agentInfoRequestDto.getUserId() + ", ResultsCount=" + baOnboardingDetailsList.size()
            ));
            if (!baOnboardingDetailsList.isEmpty()) {
                SNDAndOPResponse sndAndOPResponse = SNDAndOPResponse.builder()
                        .totalPages(baOnboardingDetailsList.size())
                        .page(1)
                        .pageSize(baOnboardingDetailsList.size())
                        .results(
                                baOnboardingDetailsList.stream()
                                        .map(
                                                baOnboardingDetails -> SNDAndOPResponse.UserResult.builder()
                                                        .userId(baOnboardingDetails.getAgentId())
                                                        .userType("Distributor BA")
                                                        .userRole(Arrays.stream(baOnboardingDetails.getRoles().split(",")).toList())
                                                        .contactNo(baOnboardingDetails.getMsisdn()) // Todo: Check if the contact phone is for the agent or the onboarder
                                                        .emailId(baOnboardingDetails.getApprovedBy()) // Todo: Check if it's the approve / onboarder or whatever
                                                        .status(baOnboardingDetails.getUserStatus() != null ? baOnboardingDetails.getUserStatus().name() : "N/A") // Todo: Check if the user status or the application
                                                        .firstName(baOnboardingDetails.getCrmFirstName())
                                                        .lastName(baOnboardingDetails.getCrmLastName())
                                                        .contractStartDate(Optional.ofNullable(baOnboardingDetails.getApprovedOn()).map(LocalDateTime::toString).orElse(null))
                                                        .contractEndDate("N/A")
                                                        .ekycBiometricId(baOnboardingDetails.getCrmBiometricsId())
                                                        .dealerId(null)
                                                        .channelType(null)
                                                        .channelSubType(null)
                                                        .salesChannelName(getChannelNameById(baOnboardingDetails.getDistributorShopId()))
                                                        .location("")
                                                        .wallet(baOnboardingDetails.getMsisdn())
                                                        .addresses(
                                                                List.of(
                                                                        SNDAndOPResponse.Address.builder()
                                                                                .addressType("NID Address")
                                                                                .woreda((baOnboardingDetails.getNidAddress() != null) ? baOnboardingDetails.getNidAddress().getWoreda() : "N/A")
                                                                                .subCity("N/A")
                                                                                .region((baOnboardingDetails.getNidAddress() != null) ? baOnboardingDetails.getNidAddress().getNidRegion() : "N/A")
                                                                                .location(baOnboardingDetails.getLatitude() + ", " + baOnboardingDetails.getLongitude())
                                                                                .build()
                                                                )
                                                        )
                                                        .rootParentAccManagerId(baOnboardingDetails.getApprovedBy())
                                                        .salesChannelStatus(baOnboardingDetails.getStatus().name())
                                                        .rootParentAccManagerName(baOnboardingDetails.getApprovedBy())
                                                        .rootParentId(
                                                                dealerInfoRepository
                                                                        .getDealerCodeByOldShortCode(
                                                                                channelRepository.getDistributorShortCodeByDistributorId(baOnboardingDetails.getDistributorShopId()).orElse("N/A"))
                                                                        .orElse("N/A")
                                                        )
                                                        .rootParentName(baOnboardingDetails.getApprovedBy())
                                                        .build()
                                        ).collect(Collectors.toList()))
                        .build();

                logManager.info(loggerCommon.getLog(log, "info", transactionId, 0, "Success!", "OP Response: " + sndAndOPResponse));
                return sndAndOPResponse;
            }
            else {
                // Prepare token request
                HttpHeaders tokenHeaders = new HttpHeaders();
                tokenHeaders.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

                MultiValueMap<String, String> tokenBody = new LinkedMultiValueMap<>();
                tokenBody.add("client_id", clientId);
                tokenBody.add("client_secret", clientSecret);
                tokenBody.add("scope", scope);
                tokenBody.add("grant_type", grantType);

                HttpEntity<MultiValueMap<String, String>> tokenRequest = new HttpEntity<>(tokenBody, tokenHeaders);

                RestTemplate restTemplate = new RestTemplate();
                ResponseEntity<Map> tokenResponse = restTemplate.postForEntity(tokenUrl, tokenRequest, Map.class);

                if (!tokenResponse.getStatusCode().is2xxSuccessful() || tokenResponse.getBody() == null) {
                    logManager.error(loggerCommon.getLog(log, "error", transactionId , -1, "Token request failed", tokenResponse.toString()));
                    return ResponseTemplate.error("User details unavailable for the provided agent account.");
                }

                String accessToken = (String) tokenResponse.getBody().get("access_token");
                if (accessToken == null || accessToken.isEmpty()) {
                    logManager.error(loggerCommon.getLog(log, "error", transactionId, -1, "Access token is empty", ""));
                }

                // Prepare SND API call
                HttpHeaders partnerHeaders = new HttpHeaders();
                partnerHeaders.setContentType(MediaType.APPLICATION_JSON);
                partnerHeaders.setBearerAuth(accessToken);

                Map<String, String> partnerBody = Collections.singletonMap("userId", agentInfoRequestDto.getUserId());
                HttpEntity<Map<String, String>> partnerRequest = new HttpEntity<>(partnerBody, partnerHeaders);

                ResponseEntity<String> partnerResponse = restTemplate.postForEntity(partnerUrl, partnerRequest, String.class);

                if (!partnerResponse.getStatusCode().is2xxSuccessful() || partnerResponse.getBody() == null) {
                    logManager.error(loggerCommon.getLog(log, "error",transactionId,  -1, "Partner API call failed", partnerResponse.toString()));
                }

                ObjectMapper mapper = new ObjectMapper();
                SNDAndOPResponse sndAndOPResponse = mapper.readValue(partnerResponse.getBody(), SNDAndOPResponse.class);

                if (sndAndOPResponse.getResults() == null || sndAndOPResponse.getResults().isEmpty()) {
                    logManager.info(loggerCommon.getLog(log, "info", transactionId, 0, "No user data found in partner response", ""));
                    return ResponseTemplate.error("User details unavailable for the provided agent account.");
                }

                logManager.info(loggerCommon.getLog(log, "info", transactionId, 0, "Success", "SnD Response: " + sndAndOPResponse));
                return sndAndOPResponse;
            }
        } catch (Exception e) {
            e.printStackTrace();
            logManager.error(loggerCommon.getLog(log, transactionId,  1, "Something went wrong! Please try again.", e));
            return ResponseTemplate.error("Something went wrong! Please try again.");
        }
    }

    private String getChannelNameById(Long id) {
        return channelsEntityRepository.findById(id).map(ChannelsEntity::getChannelName).orElse("N/A");
    }

    public ResponseTemplate<List<BasicBADto>> getReverifiedBAs() {
        try {
            List<BasicBADto> result =
                    baOnboardingDetailsRepository.findAllReverifiedBAs().stream()
                            .map(ba -> new BasicBADto(ba.getId(), ba))
                            .collect(Collectors.toList());
            return ResponseTemplate.success(result);
        } catch (Exception e) {
            //  logManager.error("Error fetching re-verified BAs", e);
            return ResponseTemplate.error("Failed to fetch re-verified BAs");
        }
    }

    public ResponseTemplate<List<BasicBADto>> getNewBAs() {
        try {
            List<BasicBADto> result =
                    baOnboardingDetailsRepository.findAllNewBAs().stream()
                            .map(ba -> new BasicBADto(ba.getId(), ba))
                            .collect(Collectors.toList());
            return ResponseTemplate.success(result);
        } catch (Exception e) {
            // logManager.error("Error fetching new BAs", e);
            return ResponseTemplate.error("Failed to fetch new BAs");
        }
    }


    //count for reverified (isReverified = true)

    public ResponseTemplate<?> getNewAgentsStatistics(LogManager logManager) {
        String transactionId = "ONE_PLATFORM_BA_ONBOARDING_" + UUID.randomUUID()+ System.currentTimeMillis();
        Log log = new Log(transactionId, "info", "Fetching new agents statistics", "", "", "",
                "ONE_PLATFORM_BA_ONBOARDING", "ONE_PLATFORM_BA_ONBOARDING",
                "0", "Default!", "Default Detailed Message.", "");

        try {
            // Count new agents (isReverified = false)
            int totalCreated = baOnboardingDetailsRepository.countByIsReverifiedFalse();
            int totalActive = baOnboardingDetailsRepository.countByIsReverifiedFalseAndStatusApproved();
            int totalPending = baOnboardingDetailsRepository.countByIsReverifiedFalseAndStatusPending();
            int totalRejected = baOnboardingDetailsRepository.countByIsReverifiedFalseAndStatusRejected();

            Map<String, Object> data = new LinkedHashMap<>();
            data.put("totalCreatedAgents", totalCreated);
            data.put("totalActiveAgents", totalActive);
            data.put("totalPendingAgents", totalPending);
            data.put("totalRejectedAgents", totalRejected);

            return ResponseTemplate.success(data);
        } catch (Exception e) {
            logManager.error(loggerCommon.getLog(log, transactionId,  1, "Something went wrong! Please try again.", e));
            return ResponseTemplate.error("Something went wrong! Please try again.");
        }
    }

    public ResponseTemplate<?> getReverifiedAgentsStatistics(LogManager
                                                                     logManager) {
        String transactionId = "ONE_PLATFORM_BA_ONBOARDING_" + System.currentTimeMillis();
        Log log = new Log(transactionId, "info", "Fetching reverified agents statistics", "", "", "",
                "ONE_PLATFORM_BA_ONBOARDING", "ONE_PLATFORM_BA_ONBOARDING",
                "0", "Default!", "Default Detailed Message.", "");

        try {
            // count reverified agents (isReverified = true)
            int totalCreated = baOnboardingDetailsRepository.countByIsReverifiedTrue();
            int totalActive = baOnboardingDetailsRepository.countByIsReverifiedTrueAndStatusApproved();
            int totalPending = baOnboardingDetailsRepository.countByIsReverifiedTrueAndStatusPending();
            int totalRejected = baOnboardingDetailsRepository.countByIsReverifiedTrueAndStatusRejected();

            Map<String, Object> data = new LinkedHashMap<>();
            data.put("totalCreatedAgents", totalCreated);
            data.put("totalActiveAgents", totalActive);
            data.put("totalPendingAgents", totalPending);
            data.put("totalRejectedAgents", totalRejected);

            return ResponseTemplate.success(data);
        } catch (Exception e) {
            logManager.error(loggerCommon.getLog(log, transactionId , 1, "Something went wrong! Please try again.", e));
            return ResponseTemplate.error("Something went wrong! Please try again.");
        }
    }

    public ResponseTemplate<?> listUsersReportWebHandler(ListUsersFilterParamsRequestDto filteringCriteria, LogManager logManager) {
        String transactionId = "ONE_PLATFORM_BA_ONBOARDING_" + UUID.randomUUID()+  System.currentTimeMillis();

        Log log = new Log(transactionId, "info", "Fetching onboarded agents on web", "", "", "", "ONE_PLATFORM_BA_ONBOARDING", "ONE_PLATFORM_BA_ONBOARDING",
                "0", "Default!", "Default Detailed Message.", "");

        try {
            String initiatorEmail = helper.extractEmailFromRequest(logManager, log , transactionId) ;

            ResponseTemplate<UserDetailsResponseDto> userDetailsResponse = commonService.getUserBasicDetails(null, initiatorEmail, internalUsersEntityRepository, externalUsersEntityRepository, logManager, log);
            if (userDetailsResponse.isSuccess()) {
                if (Helper.hasAccess(Authorization.CAN_VIEW_ALL_STATUS_USERS_WEB, userDetailsResponse.getData().getUserHierarchies())) {
                    if (filteringCriteria.getMsisdn() != null && !filteringCriteria.getMsisdn().isBlank()) {
                        ResponseTemplate<String> validatedPhoneNumberResponse = helper.validatePhoneNumber(filteringCriteria.getMsisdn());
                        if (!validatedPhoneNumberResponse.isSuccess()) {
                            return validatedPhoneNumberResponse;
                        }
                        filteringCriteria.setMsisdn(validatedPhoneNumberResponse.getData());
                    }

//                    Note:: ONBOARDER - created by
                    if (Helper.hasAccess(Authorization.CAN_ONBOARD_APP, userDetailsResponse.getData().getUserHierarchies())) {
                        if (filteringCriteria.getCreatedByList() == null || filteringCriteria.getCreatedByList().isEmpty()) {
                            filteringCriteria.setCreatedByList(List.of(initiatorEmail));
                        }
                    }
//                    Note:: RSM - approve
                    if (userDetailsResponse.getData().getUserHierarchies().contains(Constants.DISTRIBUTION_RSM_ROLE_ID)) {
                        filteringCriteria.setRsmList(List.of(initiatorEmail));
                    }
////                    Note:: RAM - approve
                    else if (userDetailsResponse.getData().getUserHierarchies().contains(Constants.RAM_ROLE_ID)) {
                        filteringCriteria.setRamList(List.of(initiatorEmail));
                    }
//                    Note:: NSM - RSM - Internal User
                    else if (userDetailsResponse.getData().getUserHierarchies().contains(Constants.DISTRIBUTION_NSM_ROLE_ID)) {
                        List<InternalUsersEntity> myRSMUsers = internalUsersEntityRepository.findAllByParentId(userDetailsResponse.getData().getId());
                        filteringCriteria.setRsmList(myRSMUsers.stream().map(InternalUsersEntity::getUserName).collect(Collectors.toSet()).stream().toList());
                    }
//                    Note:: Distributor - Shop Manager - External User
                    else if (userDetailsResponse.getData().getUserHierarchies().contains(Constants.DISTRIBUTION_ADMIN_ID)) {
                        List<ExternalUsersEntity> myDSMUsers = externalUsersEntityRepository.findAllByParentId(userDetailsResponse.getData().getId());
                        filteringCriteria.setCreatedByList(myDSMUsers.stream().map(ExternalUsersEntity::getUserName).collect(Collectors.toSet()).stream().toList());
                    }

                    System.err.println("[WEB] Filtering criteria: " + filteringCriteria);
                    Specification<BAOnboardingDetails> spec = SpecificationBuilder.buildStoreSpecification(filteringCriteria, Map.of("channel", "web"));
                    Pageable pageable = PageRequest.of(filteringCriteria.getPage(), filteringCriteria.getSize());
                    Page<BAOnboardingDetails> baOnboardingDetailsPage = baOnboardingDetailsRepository.findAll(spec, pageable);

                    Map<String, Object> responsePageable = Helper.getPageableResponse(baOnboardingDetailsPage);
                    Set<Long> distributorIdList = baOnboardingDetailsPage.getContent().stream().map(BAOnboardingDetails::getDistributorId).collect(Collectors.toSet());
                    List<ChannelsEntity> distributors = channelsEntityRepository.findAllChannelsByMultipleIds(distributorIdList);
                    List<LinkedHashMap<String, Object>> responseContentList =  baOnboardingDetailsPage.getContent().stream().map(ba -> {
                                String distributorName = distributors.stream().filter(dis -> dis.getId().equals(ba.getDistributorId())).findFirst().map(ChannelsEntity::getChannelName).orElse("N/A");
                                LinkedHashMap<String, Object> response = new LinkedHashMap<>();
                                response.put("id", ba.getId());
                                response.put("CRMFullName", Helper.formatFullName(ba.getCrmFirstName(), ba.getCrmMiddleName(), ba.getCrmLastName()));
                                response.put("applicationStatus", ba.getStatus());
                                response.put("ReasonForApplicationStatusUpdate", ba.getReasonForApplicationStatusUpdate());
                                response.put("approvedBy",ba.getApprovedBy());
                                response.put("approvedOn",ba.getApprovedOn());
                                response.put("userStatus", ba.getUserStatus());
                                response.put("reasonForUserUpdate", ba.getReasonForUserUpdate());
                                response.put("msisdn", ba.getMsisdn());
                                response.put("distributorId", ba.getDistributorId());
                                response.put("distributorShopId", ba.getDistributorShopId());
                                response.put("createdOn", ba.getCreatedOn());
                                response.put("createdBy", ba.getCreatedBy());
                                response.put("sndCreatedBy", ba.getSndCreatedBy());
                                response.put("division", ba.getDivision());
                                response.put("region", ba.getRegion());
                                response.put("cluster" , ba.getCluster());
                                response.put("latitude", ba.getLatitude());
                                response.put("longitude", ba.getLongitude());
                                response.put("updatedBy", ba.getUpdatedBy());
                                response.put("updatedOn", ba.getUpdatedOn());
                                response.put("nidfullName", Helper.formatFullName(ba.getNidFirstName(), ba.getNidMiddleName(), ba.getNidLastName()));
//                                response.put("nidRegion", ba.getNidAddress().getNidRegion());
//                                response.put("woreda" , ba.getNidAddress().getWoreda());
//                                response.put("zone" , ba.getNidAddress().getZone());
//                                response.put("kebele" , ba.getNidAddress().getKebele());
                                response.put("address" , ba.getNidAddress());
                                response.put("rsm", ba.getRsm());
                                response.put("ram", ba.getRam());
                                response.put("route", ba.getRoute());
                                response.put("site", ba.getSite());





                                response.put("isReverified", ba.getIsReverified());
                                response.put("dateOfBirth",ba.getNidDateOfBirth());
                                response.put("faydaNumber" , ba.getFaydaNumber());
                                response.put("agentId", ba.getAgentId() != null ? ba.getAgentId() : "N/A");
                                response.put("parentDistributor", distributorName);
                                response.put("reverified_on" , ba.getReverifiedOn());
                            //    response.put("letterOfConsentList", ba.getLetterOfConsentList());
                             //   response.put("nidCardPictureList", ba.getNidCardPictureList());
                                response.put("nidIDExpirationDate", ba.getNidIDExpirationDate());
                             //   response.put("nidPhotoPath", ba.getNidPhotoPath());
                             //   response.put("agentPhotoList", ba.getAgentPhotoList());
                                response.put("isAgentCreatedOnEkyc", ba.getIsAgentCreatedOnEkyc());
                                response.put("isAgentCreatedOnDxl", ba.getIsAgentCreatedOnDxl());
                                response.put("isAgentCreatedOnBD", ba.getIsAgentCreatedOnBD());



                                return response;
                            }
                    ).toList();

                    responsePageable.put("content", responseContentList);

                    return ResponseTemplate.success(responsePageable);
                }
                else {
                    logManager.info(loggerCommon.getLog(log, "info", transactionId,  1, "Unauthorized!", "User by the specified username [ " + initiatorEmail + " ] with the roles [ " + userDetailsResponse.getData().getUserHierarchies() + " ] is not authorized!"));
                    return ResponseTemplate.error("Unauthorized!");
                }
            }
            else {
                return userDetailsResponse;
            }
        } catch (Exception e) {
            e.printStackTrace();
            logManager.error(loggerCommon.getLog(log, transactionId, 1, "Something went wrong! Please try again.", e));
            return ResponseTemplate.error("Something went wrong! Please try again.");
        }
    }


}
