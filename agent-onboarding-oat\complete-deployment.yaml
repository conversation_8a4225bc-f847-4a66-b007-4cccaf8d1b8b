# Namespace
apiVersion: v1
kind: Namespace
metadata:
  name: mpesa-channels
  labels:
    name: mpesa-channels
---
# PersistentVolumeClaim
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: channels-agent-onboarding-prod-claim-v2
  namespace: mpesa-channels
  labels:
    app: agent-onboarding
    component: storage
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 10Gi
  storageClassName: standard  # Update this to match your cluster's storage class
---
# Deployment
apiVersion: apps/v1
kind: Deployment
metadata:
  namespace: mpesa-channels
  name: agent-onboarding
  labels:
    app: agent-onboarding
spec:
  replicas: 1
  selector:
    matchLabels:
      app: agent-onboarding
  template:
    metadata:
      labels:
        app: agent-onboarding
    spec:
      imagePullSecrets:
        - name: harbor-registry-key
      containers:
        - name: agent-onboarding
          image: et02-harbor.safaricomet.net/mpesa_channels/agent-onboarding-prod:46
          volumeMounts:
          - mountPath: /app/files
            name: agent-onboarding
          imagePullPolicy: IfNotPresent
          envFrom:
            - secretRef:
                name: common-default-secret
            - configMapRef:
                name: common-default-cm
          ports:
            - containerPort: 8090
      volumes:
      - name: agent-onboarding
        persistentVolumeClaim:
          claimName: channels-agent-onboarding-prod-claim-v2
---
# Service
apiVersion: v1
kind: Service
metadata:
  name: agent-onboarding-service
  namespace: mpesa-channels
  labels:
    app: agent-onboarding
    run: agent-onboarding
spec:
  ports:
    - name: http
      protocol: TCP
      port: 8090
      targetPort: 8090
  selector:
    app: agent-onboarding
