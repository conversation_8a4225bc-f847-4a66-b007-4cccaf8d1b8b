package et.safaricom.agent_onboarding.model;

import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import jakarta.persistence.Entity;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
@Embeddable
public class Address {
    @Column(name = "zone")
    private String zone;

    @Column(name = "kebele")
    private String kebele;

    @Column(name = "woreda")
    private String woreda;

    @Column(name = "nid_region")
    private String nidRegion;
}
