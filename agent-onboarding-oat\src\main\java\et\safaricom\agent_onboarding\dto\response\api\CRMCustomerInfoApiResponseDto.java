package et.safaricom.agent_onboarding.dto.response.api;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;
import lombok.experimental.SuperBuilder;

import java.util.List;

@Data
@ToString
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class CRMCustomerInfoApiResponseDto {
    @JsonProperty("code")
    private String code;

    @JsonProperty("message")
    private String message;

    @JsonProperty("customer")
    private Customer customer;

    @JsonProperty("account")
    private Account account;

    @JsonProperty("service")
    private Service service;

    @Data
    @Builder
    @ToString
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Customer {
        @JsonProperty("parts")
        private List<Part> parts;
    }

    @Data
    @Builder
    @ToString
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Account {
        @JsonProperty("parts")
        private List<Part> parts;
    }

    @Data
    @Builder
    @ToString
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Service {
        @JsonProperty("parts")
        private List<Part> parts;
    }

    @Data
    @Builder
    @ToString
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Part {
        @JsonProperty("name")
        private String name;

        @JsonProperty("value")
        private String value;
    }
}
