# /Agent info Endpoint Method

# Overview
The `/agent-info` endpoint in the `BAOnboardingController` fetches detailed information about an onboarded Business Agent (BA) by their `agent ID`. The endpoint accepts an `AgentInfoRequestDto` containing the userId of the agent and forwards the request along with the User-Agent header value to the service method `userDetailByAgentId`.

# Endpoint Details 
```java
        @PostMapping("/agent-info")

        @Operation(summary = "Onboarded Agent Detail by agent-id", tags = "BA Onboarding", description = "An API for fetching the details of onboarded agents by agent-id.")
       
        public ResponseTemplate<?> userDetailByAgentId(
      
        @Parameter(name = "User-Agent", description = "Identifies the source of the request (Mobile App or Web Browser)", example = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36", in = ParameterIn.HEADER, schema = @Schema(type = "string"))
       
        @RequestHeader(value = "User-Agent", required = false) String userAgent,
       
        @RequestBody AgentInfoRequestDto agentInfoRequestDto) {
       
        LogManager logManager = new LogManager();
       
        logManager.startClock();
           
            return baOnboardingService.userDetailByAgentId(userAgent, agentInfoRequestDto, logManager);
       
        }
```
# userDetailByAgentId Method Flow
## 1. Fetch Onboarded Agent Data from Local Repository
```java
List<BAOnboardingDetails> baOnboardingDetailsList = baOnboardingDetailsRepository.findAllByAgentId(agentInfoRequestDto.getUserId());
```

- Retrieves all onboarding details associated with the provided agentId.


## 2. Check if the user is exist on One platform system

```java
   if (!baOnboardingDetailsList.isEmpty()) {
    
                SNDAndOPResponse sndAndOPResponse = SNDAndOPResponse.builder()
                        
                        .totalPages(baOnboardingDetailsList.size())
                        
                        .page(1)
                        
                        .pageSize(baOnboardingDetailsList.size())
                        
                        .results(
                                baOnboardingDetailsList.stream()
                                        .map(
                                                baOnboardingDetails -> SNDAndOPResponse.UserResult.builder()
                                                        
                                                        .userId(baOnboardingDetails.getAgentId())
                                                        
                                                        .userType("Distributor BA")
                                                        
                                                        .userRole(Arrays.stream(baOnboardingDetails.getRoles().split(",")).toList())
                                                        
                                                        .contactNo(baOnboardingDetails.getMsisdn()) 
                                                        
                                                        .emailId(baOnboardingDetails.getApprovedBy()) 
                                                        
                                                        .status(baOnboardingDetails.getUserStatus() != null ? baOnboardingDetails.getUserStatus().name() : "N/A") 
                                                        
                                                        .firstName(baOnboardingDetails.getCrmFirstName())
                                                        
                                                        .lastName(baOnboardingDetails.getCrmLastName())
                                                        
                                                        .contractStartDate(baOnboardingDetails.getApprovedOn().toString())
                                                        
                                                        .contractEndDate("2099-12-31T02:00:00.000+03:00")
                                                        
                                                        .ekycBiometricId(baOnboardingDetails.getCrmBiometricsId())
                                                        
                                                        .dealerId(null)
                                                        
                                                        .channelType(null)
                                                        
                                                        .channelSubType(null
                                                                
                                                        .salesChannelName(getChannelNameById(baOnboardingDetails.getDistributorShopId()))
                                                                
                                                        .location("")
                                                                
                                                        .wallet(baOnboardingDetails.getMsisdn())
                                                                
                                                        .addresses(
                                                                
                                                                List.of(
                                                                        SNDAndOPResponse.Address.builder()
                                                                                
                                                                                .addressType("NID Address")
                                                                                
                                                                                .woreda((baOnboardingDetails.getNidAddress() != null) ? baOnboardingDetails.getNidAddress().getWoreda() : "N/A")
                                                                                
                                                                                .subCity("N/A")
                                                                                
                                                                                .region((baOnboardingDetails.getNidAddress() != null) ? baOnboardingDetails.getNidAddress().getNidRegion() : "N/A")
                                                                                
                                                                                .location(baOnboardingDetails.getLatitude() + ", " + baOnboardingDetails.getLongitude())
                                                                                
                                                                                .build()
                                                                )
                                                        )
                                                        .rootParentAccManagerId(baOnboardingDetails.getApprovedBy())
                                                                
                                                        .salesChannelStatus(baOnboardingDetails.getStatus().name())
                                                                
                                                        .rootParentAccManagerName(baOnboardingDetails.getApprovedBy())
                                                                
                                                        .rootParentId(channelRepository.getDistributorShortCodeByDistributorId(baOnboardingDetails.getDistributorShopId()).orElse("N/A"))
                                                         
                                                         .rootParentName(baOnboardingDetails.getApprovedBy())
                                                       
                                                        .build()
                                                                
                                        ).collect(Collectors.toList()))
                        .build();

                logManager.info(loggerCommon.getLog(log, "info", 0, "Success!", "OP Response: " + sndAndOPResponse));
                
                return ResponseTemplate.success(sndAndOPResponse);
                
            }
```
## 3. If Not Found, Prepare to Query External SND System

### A. Create HTTP headers and set content type to application/x-www-form-urlencoded
```java
    HttpHeaders tokenHeaders = new HttpHeaders();

    tokenHeaders.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
```

### Create the request body with required OAuth 2.0 parameters
```java
    MultiValueMap<String, String> tokenBody = new LinkedMultiValueMap<>();

    tokenBody.add("client_id", clientId);  
    
    tokenBody.add("client_secret", clientSecret);  
    
    tokenBody.add("scope", scope);  
    
    tokenBody.add("grant_type", grantType);
    
    HttpEntity<MultiValueMap<String, String>> tokenRequest = new HttpEntity<>(tokenBody, tokenHeaders);
``` 
- Sets headers and body for requesting an access token.


### B. Execute Token Request
```java
    RestTemplate restTemplate = new RestTemplate();

    ResponseEntity<Map> tokenResponse = restTemplate.postForEntity(tokenUrl, tokenRequest, Map.class);
```
- Obtains an access token required for authorization.

### C. Prepare SND API Request with Bearer Token
```java
    HttpHeaders partnerHeaders = new HttpHeaders();

    partnerHeaders.setContentType(MediaType.APPLICATION_JSON);
    
    partnerHeaders.setBearerAuth(accessToken);
    
    Map<String, String> partnerBody = Collections.singletonMap("userId", agentInfoRequestDto.getUserId());
    
    HttpEntity<Map<String, String>> partnerRequest = new HttpEntity<>(partnerBody, partnerHeaders);
   
    ResponseEntity<String> partnerResponse = restTemplate.postForEntity(partnerUrl, partnerRequest, String.class);

```
### D. Execute SND API Request

```java
    ResponseEntity<String> partnerResponse = restTemplate.postForEntity(partnerUrl, partnerRequest, String.class);
```
- Calls the external system to fetch agent details.

### E. Handle Unsuccessful Responses

```java
    if (!partnerResponse.getStatusCode().is2xxSuccessful() || partnerResponse.getBody() == null) {
    
        logManager.error(loggerCommon.getLog(log, "error", -1, "Partner API call failed", partnerResponse.toString()));
    }
```
- Logs errors if the external API call fails or returns empty.

## 4. Deserialize and Return the SND Response

```java
    ObjectMapper mapper = new ObjectMapper();

    SNDAndOPResponse sndAndOPResponse = mapper.readValue(partnerResponse.getBody(), SNDAndOPResponse.class);

    logManager.info(loggerCommon.getLog(log, "info", 0, "Success", "SND Response: " + sndAndOPResponse));
    
    return ResponseTemplate.success(sndAndOPResponse);
    
```

- Parses the JSON response to a typed DTO.

- Logs success and returns a structured API response.

# Summary
- The endpoint first attempts to fetch BA details from the internal repository.
- If no record exists, it requests an OAuth token and queries the external SND system.
- Results from either source are mapped into a unified response format.
- Robust logging captures success and failure scenarios for traceability.





