###############################[ PROD ]################################
#spring.datasource.url= ${OP_DB_URL}
#spring.datasource.username= ${OP_DB_USER}
#spring.datasource.password= ${OP_DB_PASS}
spring.datasource.url=***************************************************
spring.datasource.username=one_prod_user
spring.datasource.password=M2cLsCN6Sg_5ueVG

# GeoSpatial - Prod
secondary.datasource.url=*********************************************
secondary.datasource.username=hiwot_desta
secondary.datasource.password=Hiwot@MPESA20

app.one-platform.auth.jwk_set_uri= https://keycloak.dev3.safaricomet.net/realms/onePlatformDev/protocol/openid-connect/certs
spring.security.oauth2.resourceserver.jwt.jwk-set-uri= https://keycloak.dev3.safaricomet.net/realms/onePlatformDev/protocol/openid-connect/certs

# EKYC Configs
ekyc.auth.username=<EMAIL>
ekyc.auth.password=33iCX9juuUp7t3s2
ekyc.biometrics-verification.finger-type=LEFT_THUMB
ekyc.create-agent.role=ROLE_EKYC_AGENT
ekyc.create-agent.mpesa-role=ROLE_MPESA_EKYC_AGENT

# CRM Configs
crm.auth.username=OnePlatform
crm.auth.password=F/unhLLZncdKv+dqYzAX0w==
crm.x-correlation-conversationid=232322
crm.x-source-system=ONE_PLATFORM
crm.x-source-identity-token=U2FmYXJpY29tOmUyZTplc2JldDpBdXRvbWF0aW9u
x-route-id=partyaccount
x-correlation-id=generated

# SMS Configs
sms.username=mpesa-sms-p
sms.password=BBdfj&djd736!!

# Base Urls
ekyc.base-url=https://api.ekyclb.safaricomet.net
crm.base-url=https://query-token.tibcolb.safaricomet.net:8089
crm.customer-management.base-url=https://querycust-msisdn.tibcolb.safaricomet.net:8086
sms.base-url=https://dxl-proxy.safaricomet.net:8443/sendsms
dxl.ekyc.base-url=https://dxl-proxy.safaricomet.net:8443/ekyc-mpesa-registration

# Endpoints
ekyc.auth.url=${ekyc.base-url}/api/v1/auth/signin
ekyc.verification.url=${ekyc.base-url}/api/v1/verifications/id/
ekyc.verification-dedup.url=${ekyc.base-url}/api/v1/identifications/dedupe
ekyc.vetting.url=${ekyc.base-url}/api/v1/agents/verified/
ekyc.create-agent.url=${ekyc.base-url}/api/v1/agents

crm-auth.url=${crm.base-url}/v1/token
crm.customer-info.url=${crm.customer-management.base-url}/auth/v1/customerManagement/customer-info?serviceId=

dxl.add-ekyc-agent.url=${dxl.ekyc.base-url}/ekyc/add-ekyc-agent

dxl.send-sms.url=${sms.base-url}/send-sms

# Keycloak Token Endpoint for snd
snd.app.keycloak.token-url=https://keycloak.sd.blueprint.lab/auth/realms/Snd/protocol/openid-connect/token

# SND Details API Endpoint
snd.app.partner.details-url=http://gui.sd.blueprint.lab/api/v1/partner/details?page=1&size=10
auth.client-id=ctapp
auth.client-secret=hGhgESkjBQqGh1wM7PwciamW0M56w5ZF
auth.scope=email
auth.grant-type=client_credentials
