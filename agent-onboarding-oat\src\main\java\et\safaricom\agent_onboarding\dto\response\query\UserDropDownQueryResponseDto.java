package et.safaricom.agent_onboarding.dto.response.query;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import et.safaricom.agent_onboarding.enums.UM_USER_STATUS;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

@Data
@ToString
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class UserDropDownQueryResponseDto {
    @JsonIgnore
    @JsonProperty("firstName")
    private String firstName;

    @JsonIgnore
    @JsonProperty("middleName")
    private String middleName;

    @JsonIgnore
    @JsonProperty("lastName")
    private String lastName;

    @JsonProperty("fullName")
    private String fullName;

    @JsonProperty("username")
    private String username;

    @JsonIgnore
    @JsonProperty("isActive")
    private Boolean isActive;

    @JsonIgnore
    @JsonProperty("status")
    private UM_USER_STATUS status;

    public UserDropDownQueryResponseDto(String firstName, String middleName, String lastName, String username, Boolean isActive, UM_USER_STATUS status) {
        this.firstName = firstName;
        this.middleName = middleName;
        this.lastName = lastName;
        this.username = username;
        this.isActive = isActive;
        this.status = status;
    }
}
