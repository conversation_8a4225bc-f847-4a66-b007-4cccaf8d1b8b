package et.safaricom.agent_onboarding.repository.view;

import et.safaricom.agent_onboarding.model.view.ChannelsEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Arrays;
import java.util.List;
import java.util.Set;

@Repository
public interface ChannelsEntityRepository extends JpaRepository<ChannelsEntity, Long> {
    @Query("SELECT ch FROM ChannelsEntity AS ch WHERE ch.id = ch.parentId AND ch.id BETWEEN 1 AND 23 OR ch.id = 582")
    List<ChannelsEntity> findAllDistributors();

    @Query("SELECT ch FROM ChannelsEntity AS ch WHERE ch.parentId = :distributorId")
    List<ChannelsEntity> findAllShops(@Param("distributorId") Long distributorId);

    @Query("SELECT ch FROM ChannelsEntity AS ch WHERE ch.id in :distributorIdList")
    List<ChannelsEntity> findAllChannelsByMultipleIds(@Param("distributorIdList") Set<Long> distributorIdList);
}
