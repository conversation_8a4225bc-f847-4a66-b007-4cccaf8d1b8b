package et.safaricom.agent_onboarding.service;

import com.mpesautils.logmanager.Log;
import com.mpesautils.logmanager.LogManager;
import et.safaricom.agent_onboarding.Logging.LoggingHelper;
import et.safaricom.agent_onboarding.dto.response.UserDetailsResponseDto;
import et.safaricom.agent_onboarding.enums.APPLICATION_STATUS;
import et.safaricom.agent_onboarding.model.BAOnboardingDetails;
//import et.safaricom.agent_onboarding.model.view.snd.SalesUserDetail;
import et.safaricom.agent_onboarding.model.view.snd.SalesChannelMaster;
import et.safaricom.agent_onboarding.model.view.snd.SalesUserDetailNew;
import et.safaricom.agent_onboarding.repository.BAOnboardingDetailsRepository;
import et.safaricom.agent_onboarding.repository.view.ExternalUsersEntityRepository;
import et.safaricom.agent_onboarding.repository.view.InternalUsersEntityRepository;
import et.safaricom.agent_onboarding.repository.view.snd.SalesChannelMasterRepository;
import et.safaricom.agent_onboarding.repository.view.snd.SalesUserDetailRepositoryNew;
import et.safaricom.agent_onboarding.repository.view.snd.SalesUserStatusMasterRepository;
import et.safaricom.agent_onboarding.utils.Authorization;
import et.safaricom.agent_onboarding.utils.Helper;
import et.safaricom.agent_onboarding.utils.LoggerCommon;
import et.safaricom.agent_onboarding.utils.ResponseTemplate;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DataAccessException;
import org.springframework.stereotype.Service;

import java.util.*;

@Slf4j
@Service
@AllArgsConstructor
public class SecondaryVerificationService {
    private final Helper helper;

    private final LoggerCommon loggerCommon;
    private final CommonService commonService;

    private final SalesUserDetailRepositoryNew salesUserDetailRepositoryNew;

    private final BAOnboardingDetailsRepository baOnboardingDetailsRepository;
    private final InternalUsersEntityRepository internalUsersEntityRepository;
    private final ExternalUsersEntityRepository externalUsersEntityRepository;
    private final SalesUserStatusMasterRepository salesUserStatusMasterRepository;
    private final SalesChannelMasterRepository salesChannelMasterRepository;


public ResponseTemplate<?> getNewSnDBAInformation(String agentId, LogManager logManager) {
    String transactionId = "ONE_PLATFORM_BA_ONBOARDING_" + UUID.randomUUID() + System.currentTimeMillis();

    LoggingHelper.setTransactionId(transactionId);
    LoggingHelper.setProcess("SND_BA_INFO_REQUEST");
    LoggingHelper.setOutcome("START");
    log.info("business.event=SND_BA_INFO_REQUEST_START agentId={} transactionId={} httpStatus={}", agentId, transactionId, 100); // 100 = processing start

    Log auditLog = new Log(transactionId, "info", "Fetching BA Information from SnD Dump", "", "", "",
            "ONE_PLATFORM_BA_ONBOARDING", "ONE_PLATFORM_SnD_DUMP", "0", "Default!", "Default Detailed Message.", "");

    try {
        String initiatorEmail = helper.extractEmailFromRequest(logManager, auditLog , transactionId);
        ResponseTemplate<UserDetailsResponseDto> userDetailsResponse = commonService.getUserBasicDetails(
                null, initiatorEmail, internalUsersEntityRepository, externalUsersEntityRepository, logManager, auditLog);

        if (userDetailsResponse.isSuccess()) {
            if (Helper.hasAccess(Authorization.CAN_ONBOARD_APP, userDetailsResponse.getData().getUserHierarchies())) {
                Optional<BAOnboardingDetails> baOnboardingDetailsOptional = baOnboardingDetailsRepository.findByAgentId(agentId);
                if (baOnboardingDetailsOptional.isPresent() &&
                        (baOnboardingDetailsOptional.get().getStatus().equals(APPLICATION_STATUS.APPROVED) ||
                                baOnboardingDetailsOptional.get().getStatus().equals(APPLICATION_STATUS.PENDING))) {

                    LoggingHelper.setOutcome("FAILURE");
                    LoggingHelper.setResponseCode("BA_ALREADY_VERIFIED");
                    log.info("business.event=SND_BA_INFO_REQUEST_FAILURE agentId={} transactionId={} reason={} httpStatus={}",
                            agentId, transactionId, "Already verified or pending", 409); // 409 Conflict
                    return ResponseTemplate.error("An agent by the specified id has already been re-verified or in the process!");
                }

                Optional<SalesUserDetailNew> sndUserDetailOptional = salesUserDetailRepositoryNew.findByUserId(agentId);
                if (sndUserDetailOptional.isPresent()) {
                    Map<String, Object> response = new LinkedHashMap<>();
                    response.put("fullName", sndUserDetailOptional.get().getName());
                    response.put("userId", sndUserDetailOptional.get().getUserId());
                    response.put("division", sndUserDetailOptional.get().getAgentDivision());
                    response.put("region", sndUserDetailOptional.get().getAgentRegion());
                    response.put("cluster", sndUserDetailOptional.get().getAgentCluster());
                    response.put("createdBy", sndUserDetailOptional.get().getCreatedBy());
                    response.put("onboardedDate", sndUserDetailOptional.get().getOnboardingDate());
                    response.put("msisdn", sndUserDetailOptional.get().getContactNumber());
                    response.put("userCategory", sndUserDetailOptional.get().getUserType());

                    List<SalesChannelMaster> salesChannelMasterList = salesChannelMasterRepository.findAllByUserId(sndUserDetailOptional.get().getUserId());
                    boolean salesChannelMasterFound = !salesChannelMasterList.isEmpty() && salesChannelMasterList.get(0).getParentId() != null;
                    Long parentId = salesChannelMasterFound ? ((Double) salesChannelMasterList.get(0).getParentId()).longValue() : null;
                    List<SalesChannelMaster> parentSalesChannelMasterList =
                            salesChannelMasterFound ? salesChannelMasterRepository.findAllBySalesChannelId(parentId) : List.of();
                    response.put("parentDistributorName", !parentSalesChannelMasterList.isEmpty() ? parentSalesChannelMasterList.get(0).getSalesChannelName() : "N/A");
                    response.put("areaSalesManager", sndUserDetailOptional.get().getDistributor());
                    response.put("userstatus", sndUserDetailOptional.get().getUserStatus());

                    LoggingHelper.setOutcome("SUCCESS");
                    LoggingHelper.setResponseCode("SND_BA_INFO_REQUEST_SUCCESS");
                    log.info("business.event=SND_BA_INFO_REQUEST_SUCCESS agentId={} transactionId={} httpStatus={}", agentId, transactionId, 200);

                    logManager.info(loggerCommon.getLog(auditLog, "info", 0, "Success!", "SnD Simple Response: " + response));
                    return ResponseTemplate.success(response);
                } else {
                    LoggingHelper.setOutcome("FAILURE");
                    LoggingHelper.setResponseCode("AGENT_NOT_FOUND");
                    log.info("business.event=SND_BA_INFO_REQUEST_FAILURE agentId={} transactionId={} reason={} httpStatus={}", agentId, transactionId, "Not found", 404);

                    logManager.info(loggerCommon.getLog(auditLog, "info", 1, "An agent by the specified id not found!", "An Agent by the specified id [ " + agentId + " ] is not found!"));
                    return ResponseTemplate.error("An agent by the specified id not found!");
                }
            } else {
                LoggingHelper.setOutcome("FAILURE");
                LoggingHelper.setResponseCode("UNAUTHORIZED");
                log.info("business.event=SND_BA_INFO_REQUEST_FAILURE agentId={} transactionId={} reason={} httpStatus={}", agentId, transactionId, "Unauthorized", 401);

                logManager.info(loggerCommon.getLog(auditLog, "info", 1, "Unauthorized!", "User by the specified username [ " + initiatorEmail + " ] with the roles [ " + userDetailsResponse.getData().getUserHierarchies() + " ] is not authorized!"));
                return ResponseTemplate.error("Unauthorized!");
            }
        } else {
            return userDetailsResponse;
        }

    } catch (Exception e) {
        LoggingHelper.setOutcome("FAILURE");
        LoggingHelper.setResponseCode("EXCEPTION");
        log.info("business.event=SND_BA_INFO_REQUEST_FAILURE agentId={} transactionId={} exception={} httpStatus={}", agentId, transactionId, e.getMessage(), 500);

        e.printStackTrace();
        logManager.error(loggerCommon.getLog(auditLog, 1, "Something went wrong! Please try again.", e));
        return ResponseTemplate.error("Something went wrong! Please try again.");
    }
}



}








