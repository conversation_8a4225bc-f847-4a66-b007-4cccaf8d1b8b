package et.safaricom.agent_onboarding.dto.request.filter;

import com.fasterxml.jackson.annotation.JsonProperty;
import et.safaricom.agent_onboarding.enums.APPLICATION_STATUS;
import et.safaricom.agent_onboarding.enums.USER_STATUS;
import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.SuperBuilder;

import java.time.LocalDate;
import java.util.List;

@Data
@ToString
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class ListUsersFilterParamsRequestDto {
    @JsonProperty("agentId")
    private String agentId;

    @JsonProperty("msisdn")
    private String msisdn;

    @JsonProperty("rsm")
    private String rsm;

    @JsonProperty("ram")
    private String ram;

    @JsonProperty("division")
    private String division;

    @JsonProperty("region")
    private String region;

    @JsonProperty("isReverified")
    private Boolean isReverified;

    @JsonProperty("applicationStatus")
    @Schema(description = "The value of the Application Status is limited to [ DRAFT || PENDING || REJECTED || REQUIRED || APPROVED ].", example = "APPROVED")
    private APPLICATION_STATUS applicationStatus;

    @JsonProperty("userStatus")
    @Schema(description = "The value of the User Status is limited to [ SUSPENDED || TERMINATED ].", example = "TERMINATED")
    private USER_STATUS userStatus;

    @JsonProperty("startDate")
    private LocalDate startDate;

    @JsonProperty("endDate")
    private LocalDate endDate;

    @JsonProperty(value = "page", required = true)
    @Schema(description = "Page is required! Initial page value - 0", example = "0")
    private Integer page;

    @JsonProperty(value = "size", required = true)
    @Schema(description = "Size is required! Initial size value - 1", example = "1")
    private Integer size;


//  Note: For internal processing
    @Hidden
    @JsonProperty("rsmList")
    private List<String> rsmList;

    @Hidden
    @JsonProperty("ramList")
    private List<String> ramList;

    @Hidden
    @JsonProperty("createdByList")
    private List<String> createdByList;
}
