package et.safaricom.agent_onboarding.repository.view.snd;

import et.safaricom.agent_onboarding.model.view.snd.SalesChannelMaster;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface SalesChannelMasterRepository extends JpaRepository<SalesChannelMaster, Long> {
    List<SalesChannelMaster> findAllByUserId(String userId);
    List<SalesChannelMaster> findAllBySalesChannelId(Long salesChannelId);
}
