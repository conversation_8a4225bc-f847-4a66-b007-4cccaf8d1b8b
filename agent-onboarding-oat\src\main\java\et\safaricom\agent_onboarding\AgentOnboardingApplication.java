package et.safaricom.agent_onboarding;

import io.swagger.v3.oas.annotations.OpenAPIDefinition;
import io.swagger.v3.oas.annotations.enums.SecuritySchemeIn;
import io.swagger.v3.oas.annotations.enums.SecuritySchemeType;
import io.swagger.v3.oas.annotations.info.Contact;
import io.swagger.v3.oas.annotations.info.Info;
import io.swagger.v3.oas.annotations.security.SecurityScheme;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

@SpringBootApplication
@OpenAPIDefinition(
		info = @Info(title = "Agent Onboarding",
				version = "1.0",
				contact = @Contact(
						name = "The Developer",
						url = "mailto:<EMAIL>",
						email = "<EMAIL>"
				),
				description = """
							The redesigned agent onboarding process aims to create a streamlined and secure system for onboarding new agents into the organization by integrating to Ethiopian national id.
							It ensures thorough identity verification, compliance with regulations, and clear communication of expectations to agents. By prioritizing efficiency, accuracy, and security, the process facilitates organizational growth and success.
						""")
)
@SecurityScheme(name = "One Platform Token", scheme = "bearer", type = SecuritySchemeType.HTTP, in = SecuritySchemeIn.HEADER)
public class AgentOnboardingApplication {

	public static void main(String[] args) {
		SpringApplication.run(AgentOnboardingApplication.class, args);
	}

}
