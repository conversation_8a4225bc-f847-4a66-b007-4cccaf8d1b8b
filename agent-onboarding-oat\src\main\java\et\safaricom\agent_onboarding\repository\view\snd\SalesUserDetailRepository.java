package et.safaricom.agent_onboarding.repository.view.snd;

import et.safaricom.agent_onboarding.model.view.snd.SalesUserDetail;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface SalesUserDetailRepository extends JpaRepository<SalesUserDetail, String> {
    Optional<SalesUserDetail> findByUserId(String userId);

    List<SalesUserDetail> findAllByUserId(String userId);
}
