# Agent Onboarding Microservice API Documentation

This document provides a comprehensive overview of all endpoints available in the Agent Onboarding microservice, including details on what they point to (database operations, external API calls) and their functionality.

## Table of Contents

- [Agent Onboarding Microservice API Documentation](#agent-onboarding-microservice-api-documentation)
  - [Table of Contents](#table-of-contents)
  - [BA Onboarding Endpoints](#ba-onboarding-endpoints)
    - [POST Endpoints](#post-endpoints)
    - [GET Endpoints](#get-endpoints)
  - [File Management Endpoints](#file-management-endpoints)
  - [Other Utility Endpoints](#other-utility-endpoints)
  - [Implementation Details](#implementation-details)
    - [Required Fields Endpoint](#required-fields-endpoint)
    - [Agent Detail Endpoints](#agent-detail-endpoints)
  - [External API Integrations](#external-api-integrations)
    - [eKYC Integration](#ekyc-integration)
    - [CRM Integration](#crm-integration)
    - [SMS Integration](#sms-integration)
  - [Database Connections](#database-connections)
  - [Kafka Integration](#kafka-integration)

## BA Onboarding Endpoints

Base Path: `/api/v1/ba-onboarding`

### POST Endpoints

| Endpoint | Description | Request Parameters | Data Flow |
|----------|-------------|-------------------|----------|
| `/create` | Initiates BA onboarding process | `BAOnboardingInitiatorRequestDto` | Saves data to database, may trigger eKYC API calls for verification |
| `/re-verify` | Initiates BA re-verification process | `BAReverificationInitiatorRequestDto` | Updates existing records in database, triggers eKYC verification |
| `/update` | Updates BA onboarding details | `BAOnboardingUpdateRequestDto`, `User-Agent` header, optional `options` parameter | Updates existing records in database |
| `/agent-info` | Fetches agent details by agent-id | `AgentInfoRequestDto`, optional `User-Agent` header | Queries database for agent information |

### GET Endpoints

| Endpoint | Description | Request Parameters | Data Flow |
|----------|-------------|-------------------|----------|
| `/snd-info` | Gets SnD BA information | `agent-id` query parameter | Queries database for SnD BA information |
| `/list` | Lists all onboarded agents | `User-Agent` header, `ListUsersFilterParamsRequestDto` | Queries database for all onboarded agents with filtering |
| `/detail` | Fetches details of an onboarded agent | `User-Agent` header, `id` query parameter | Queries database for specific agent details |
| `/required-fields` | Fetches required fields for an onboarded agent | `id` query parameter | Queries database for field requirements and returns fields that need updating |
| `/statistics` | Fetches dashboard statistics | Optional `options` query parameter of type `STATISTICS_TYPE` | Aggregates data from database for dashboard |
| `/statistics/new-agents` | Retrieves new agent statistics | None | Aggregates data from database for new agents |
| `/statistics/reverified-agents` | Retrieves reverified agent statistics | None | Aggregates data from database for reverified agents |
| `/reverified` | Gets re-verified BAs | None | Queries database for reverified BAs |
| `/new` | Gets new BAs | None | Queries database for new BAs |

## File Management Endpoints

Base Path: `/api/v1/ba-onboarding/file`

| Endpoint | Method | Description | Request Parameters | Data Flow |
|----------|--------|-------------|-------------------|----------|
| `/upload` | POST | Uploads a file using MultipartFile | `id`, `fileType` (FILE_GROUP), `file` (MultipartFile) | Stores file in the file system at location specified by `ba-onboarding.file-upload.dir` property |
| `/upload/base64` | POST | Uploads a file using Base64 encoded string | `UploadFileByBase46RequestDto` | Decodes Base64 string and stores file in the file system |
| `/download` | GET | Downloads a file | `filePath`, `requiredFileType` (REQUIRED_FILE_TYPE) | Retrieves file from the file system |

## Other Utility Endpoints

Base Path: `/api/v1/ba-onboarding/other`

| Endpoint | Method | Description | Request Parameters | Data Flow |
|----------|--------|-------------|-------------------|----------|
| `/drop-down/users` | GET | Fetches users by role | `userRole` (DROPDOWN_USER_ROLE), optional `isLocationSpecific` | Queries database for users with specific roles |
| `/drop-down/distributors` | GET | Fetches distributors and distributor shops | Optional `distributorId`, optional `isLocationSpecific` | Queries database for distributor information |
| `/drop-down/geo-spatial/location` | GET | Fetches geo-spatial locations based on hierarchy | Optional `division`, `region`, `cluster` | Queries secondary database (GeoSpatial DB) for location data |
| `/drop-down/required-field/remarks` | GET | Fetches pre-defined remarks for required fields | None | Queries database for predefined remarks |
| `/test/kafka/send-message` | GET | Tests Kafka message production | `id` | Sends test message to Kafka topic specified in configuration |

## Implementation Details

### Required Fields Endpoint

The `/required-fields` endpoint is particularly important for the mobile application. It retrieves fields that need to be updated for a specific agent onboarding record. The implementation:

1. Authenticates the user and checks for `CAN_ONBOARD_APP` authorization
2. Retrieves the BA onboarding details by ID
3. Verifies that the requester is the same user who created the record
4. Extracts the latest update request from `boUpdatableFieldList`
5. Organizes the required fields by page (using `BA_ONBOARDING_APP_PAGES` enum)
6. Returns a structured response with:
   - The onboarding ID
   - The latest remark
   - Content organized by page, including the session ID, page ID, and field data

### Agent Detail Endpoints

The system provides two ways to retrieve agent details:

1. `/detail` - Retrieves by internal database ID
2. `/agent-info` - Retrieves by agent ID (external identifier)

Both endpoints support different user interfaces (web and mobile) through the `User-Agent` header.

## External API Integrations

The microservice integrates with several external systems through the `APIGatewayService`:

### eKYC Integration

| Method | Purpose | External Endpoint |
|--------|---------|-------------------|
| `eKycAuth` | Authentication with eKYC system | `${ekyc.base-url}/api/v1/auth/signin` |
| `eKycBiometricDeDup` | Biometric deduplication | `${ekyc.base-url}/api/v1/identifications/dedupe` |
| `eKycBiometricIdDeDup` | Biometric ID deduplication | `${ekyc.base-url}/api/v1/identifications/dedupe` |
| `eKycVerification` | Verification of biometrics | `${ekyc.base-url}/api/v1/verifications/id/` |
| `eKycVetting` | Agent vetting | `${ekyc.base-url}/api/v1/agents/verified/` |
| `eKycCreateAgent` | Create agent in eKYC system | `${ekyc.base-url}/api/v1/agents` |
| `eKycUpdateAgent` | Update agent in eKYC system | `${ekyc.base-url}/api/v1/agents` |
| `dxlEKycCreateAgent` | Create agent through DXL | `${dxl.ekyc.base-url}/ekyc/add-ekyc-agent` |

### CRM Integration

| Method | Purpose | External Endpoint |
|--------|---------|-------------------|
| `crmAuth` | Authentication with CRM system | `${crm.base-url}/v1/token` |
| `crmCustomerInfo` | Fetch customer information | `${crm.customer-management.base-url}/auth/v1/customerManagement/customer-info` |

### SMS Integration

| Method | Purpose | External Endpoint |
|--------|---------|-------------------|
| `sendSms` | Send SMS notifications | `${sms.base-url}/send-sms` |

## Database Connections

The microservice connects to two databases:

1. **Primary Database (PostgreSQL)**
   - URL: `***************************************************`
   - Used for: Storing all BA onboarding related data

2. **Secondary Database (GeoSpatial)**
   - URL: `*********************************************`
   - Used for: Geo-spatial location data

## Kafka Integration

The microservice produces messages to Kafka:

- Bootstrap Servers: `10.4.42.65:9094,10.4.42.68:9094,10.4.42.59:9094`
- Default Topic: `Agent-Registration-Details`
- Security Protocol: `SASL_PLAINTEXT`
- SASL Mechanism: `SCRAM-SHA-512`