package et.safaricom.agent_onboarding.model.view.OP;

import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.Immutable;
import org.hibernate.annotations.Subselect;

import java.time.LocalDateTime;

@Getter
@Setter
@NoArgsConstructor
@Immutable
@Entity
@Subselect("SELECT * FROM usersandcluster.tbl_channels WHERE id = parent_id")
public class TblChannelView {

    @Id
    @Column(name = "id")
    private Long id;

    @Column(name = "channel_name")
    private String channelName;

    @Column(name = "channel_uid")
    private String channelUid;

    @Column(name = "created_by")
    private String createdBy;

    @Column(name = "created_date")
    private LocalDateTime createdDate;

    @Column(name = "external_org_id")
    private String externalOrgId;

    @Column(name = "is_active")
    private Boolean isActive;

    @Column(name = "is_da_created")
    private Boolean isDaCreated;

    @Column(name = "is_exported")
    private Boolean isExported;

    @Column(name = "location_id")
    private Long locationId;

    @Column(name = "parent_id")
    private Long parentId;

    @Column(name = "short_code")
    private String shortCode;

    @Column(name = "status")
    private String status;

    @Column(name = "updated_by")
    private String updatedBy;

    @Column(name = "updated_date")
    private LocalDateTime updatedDate;
}
