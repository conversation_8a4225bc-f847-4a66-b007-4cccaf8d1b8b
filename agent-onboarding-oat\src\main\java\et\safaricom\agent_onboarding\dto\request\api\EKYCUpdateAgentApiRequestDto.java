package et.safaricom.agent_onboarding.dto.request.api;

import com.fasterxml.jackson.annotation.JsonProperty;
import et.safaricom.agent_onboarding.enums.EKYC_USER_STATUS;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

import java.util.List;

@Data
@ToString
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class EKYCUpdateAgentApiRequestDto {
    @JsonProperty("agentId")
    private String agentId;

    // 1 - AGENT_SUSPENDED
    // 2 - AGENT_RESTORED
    // 3 - AGENT_TERMINATED
    // 4 - AGENT_UPDATED (when you need to update the roles)
    @JsonProperty("setStatus")
    private String setStatus;

    @JsonProperty("roles")
    private List<String> roles;

    @JsonProperty("timestamp")
    private String timestamp;
}
