package et.safaricom.agent_onboarding.repository.view;

import et.safaricom.agent_onboarding.enums.UM_USER_STATUS;
import et.safaricom.agent_onboarding.model.view.ExternalUsersEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface ExternalUsersEntityRepository extends JpaRepository<ExternalUsersEntity, Long> {

    @Query("SELECT ex FROM ExternalUsersEntity AS ex WHERE ex.userName = :username")
    Optional<ExternalUsersEntity> findByUserName(@Param("username") String username);

    @Query("SELECT exter FROM ExternalUsersEntity AS exter WHERE exter.parentId = :parentId")
    List<ExternalUsersEntity> findAllByParentId(@Param("parentId") Long parentId);
}
