package et.safaricom.agent_onboarding.geo.view;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import lombok.Getter;
import lombok.ToString;
import org.hibernate.annotations.*;
import org.locationtech.jts.geom.Geometry;

@Getter
@Entity
@ToString
@Immutable
@JsonIgnoreProperties(ignoreUnknown = true)
@Subselect("""
        SELECT cl."Cluster_ID" AS "Cluster_ID", cl.geom, cl."Cluster" AS "Cluster", cl."RAM_Base_City" AS "RAM_Base_City", cl."NSM_Division" AS "NSM_Division", cl."Distributor" AS "Distributor" from public."Clusters_LDB" AS cl
""")
@Synchronize("ClustersLDB")
@Cache(usage = CacheConcurrencyStrategy.READ_ONLY)
public class ClustersLDB {
    @Id
    @Column(name = "\"Cluster_ID\"", nullable = false)
    private String clusterID;

    @Column(name = "geom")
    private Geometry geom;

    @Column(name = "\"Cluster\"")
    private String cluster;

    @Column(name = "\"RAM_Base_City\"")
    private String ramBaseCity;

    @Column(name = "\"NSM_Division\"")
    private String nsmDivision;

    @Column(name = "\"Distributor\"")
    private String distributor;

}
