package et.safaricom.agent_onboarding.geo.view;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import lombok.Getter;
import org.hibernate.annotations.*;
import org.locationtech.jts.geom.Point;

@Getter
@Entity
@Immutable
@JsonIgnoreProperties(ignoreUnknown = true)
@Subselect("""
        SELECT ml.id, ml.geom, ml."Site_ID" as "Site_ID", ml."Dist_Cluster_ID" AS "Dist_Cluster_ID", ml."Dist_Cluster_Name" AS "Dist_Cluster_Name"
        FROM public."MasterLocation" AS ml
""")
@Synchronize("MasterLocation")
@Cache(usage = CacheConcurrencyStrategy.READ_ONLY)
public class MasterLocation {

    @Id
    @Column(name = "id", nullable = false)
    private Integer id;

    @Column(columnDefinition = "geometry(Point, 4326)", name = "geom")
    private Point geom;

    @Column(name = "\"Site_ID\"")
    private Integer siteId;

    @Column(name = "\"Dist_Cluster_ID\"")
    private String distClusterId;

    @Column(name = "\"Dist_Cluster_Name\"")
    private String distClusterName;
}

