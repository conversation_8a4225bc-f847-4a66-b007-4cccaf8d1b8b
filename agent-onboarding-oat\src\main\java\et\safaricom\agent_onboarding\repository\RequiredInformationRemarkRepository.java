package et.safaricom.agent_onboarding.repository;

import et.safaricom.agent_onboarding.model.RequiredInformationRemark;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface RequiredInformationRemarkRepository extends JpaRepository<RequiredInformationRemark, Long> {

    List<RequiredInformationRemark> findAllByIsActive(Boolean isActive);
}
