package et.safaricom.agent_onboarding.repository.view.snd;

import et.safaricom.agent_onboarding.model.view.snd.SalesUserStatusMaster;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface SalesUserStatusMasterRepository extends JpaRepository<SalesUserStatusMaster, Integer> {
    Optional<SalesUserStatusMaster> findByStatusId(Integer statusId);
}
