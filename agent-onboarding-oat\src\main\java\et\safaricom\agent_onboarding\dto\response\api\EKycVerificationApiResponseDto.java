package et.safaricom.agent_onboarding.dto.response.api;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

import java.util.List;

@Data
@ToString
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class EKycVerificationApiResponseDto {
    @JsonProperty("status")
    private String status;

    @JsonProperty("decision")
    private Boolean decision;

    @JsonProperty("data")
    private List<String> data;
}
