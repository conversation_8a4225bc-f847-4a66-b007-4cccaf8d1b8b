package et.safaricom.agent_onboarding.dto.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import et.safaricom.agent_onboarding.enums.APPLICATION_STATUS;
import et.safaricom.agent_onboarding.enums.BIOMETRICS_TYPE;
import et.safaricom.agent_onboarding.enums.NID_PICTURE_TYPE;
import et.safaricom.agent_onboarding.enums.USER_STATUS;
import et.safaricom.agent_onboarding.model.BiometricsData;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

import java.time.LocalDate;
import java.util.List;
import java.util.UUID;

@Data
@ToString
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class BAOnboardingUpdateRequestDto {
    @JsonProperty(value = "id")
    public Long id;

    @JsonProperty(value = "sessionId")
    public UUID sessionId;

    @JsonProperty("biometricsList")
    public List<BiometricsData> biometricsList;

    @JsonProperty("agentPhotoList")
    public List<String> agentPhotoList;

    @JsonProperty("nidCardPictureList")
    public List<NIDCardPicture> nidCardPictureList;


    @JsonProperty("nidFullName")
    public String nidFullName;

    @JsonProperty("nidSubjectId")
    public String nidSubjectId;

    @JsonProperty("nidDateOfBirth")
    public LocalDate nidDateOfBirth;

    @JsonProperty("nidIDExpirationDate")
    public LocalDate nidIDExpirationDate;

    @JsonProperty("nidAddress")
    public Address nidAddress;

    @JsonProperty("nidPhotoPath")
    public String nidPhotoPath;

    @JsonProperty("letterOfConsentList")
    public List<String> letterOfConsentList;


    @JsonProperty("rsmUsername")
    private String rsmUsername;

    @JsonProperty("ramUsername")
    private String ramUsername;

    @JsonProperty("distributorId")
    private Long distributorId;

    @JsonProperty("distributorShopId")
    private Long distributorShopId;

    @JsonProperty("latitude")
    private Double latitude;

    @JsonProperty("longitude")
    private Double longitude;

    @JsonProperty("division")
    private String division;

    @JsonProperty("region")
    private String region;

    @JsonProperty("cluster")
    private String cluster;

    @JsonProperty("route")
    private String route;

    @JsonProperty("site")
    private String site;

    @JsonProperty("applicationStatus")
    private APPLICATION_STATUS applicationStatus;

    @JsonProperty("updatableFields")
    private List<String> updatableFields;

    @JsonProperty("remark")
    private String remark;

    @JsonProperty("userStatus")
    private USER_STATUS userStatus;

    @JsonProperty("reasonForUserUpdate")
    private String reasonForUserUpdate;

    @JsonProperty("reasonForApplicationStatusUpdate")
    private String reasonForApplicationStatusUpdate;



    @Data
    @ToString
    @SuperBuilder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class BiometricsRequestData {
        @JsonProperty("biometricsFilePath")
        private String biometricsFilePath;

        @JsonProperty("biometricsType")
        private BIOMETRICS_TYPE biometricsType;

        @JsonProperty("biometricsSubType")
        private String biometricsSubType;
    }

    @Data
    @ToString
    @SuperBuilder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class NIDCardPicture {
        @JsonProperty("nidCardPicturePath")
        private String nidCardPicturePath;

        @JsonProperty("nidCardPictureType")
        private NID_PICTURE_TYPE nidCardPictureType;
    }

    @Data
    @ToString
    @SuperBuilder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Address {
        @JsonProperty("zone")
        private String zone;

        @JsonProperty("kebele")
        private String kebele;

        @JsonProperty("woreda")
        private String woreda;

        @JsonProperty("region")
        private String region;
    }

    @JsonProperty("isDone")
    private Boolean isDone;
}
