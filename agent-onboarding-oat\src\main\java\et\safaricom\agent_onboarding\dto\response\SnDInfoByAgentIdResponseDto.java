package et.safaricom.agent_onboarding.dto.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.List;

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class SnDInfoByAgentIdResponseDto {
    private int totalPages;
    private int page;
    private int pageSize;
    private List<UserResult> results;

    @Data
    @SuperBuilder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class UserResult {
        private String userId;
        private String userType;
        private List<String> userRole;
        private String contactNo;
        private String emailId;
        private String status;
        private String firstName;
        private String lastName;
        private String parentUserId;
        private String contractStartDate;
        private String contractEndDate;
        private String ekycBiometricId;
        private String dealerId;
        private String channelType;
        private String channelSubType;
        private String salesChannelName;
        private String region;
        private String sales_area;
        private String cluster;
        private String route;
        private String location;
        private String wallet;
        private List<Address> addresses;
        private String parentSalesChannelName;
        private Long parentSalesChannelId;
        private String rootParentAccManagerId;
        private String salesChannelStatus;
        private String parentAssignedAsm;
        private String rootParentAccManagerName;
        private String rootParentId;
        private String rootParentName;
    }

    @Data
    @SuperBuilder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Address {
        private String addressType;
        private String woreda;
        private String subCity;
        private String region;
        private String zone;
        private String location;
    }
}
