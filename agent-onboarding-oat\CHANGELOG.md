# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## / [1.0.1] - 2025-8-18
### Added
- **`initiateBAOnboardingOrReverification`**:
    - Added `Is-Reverified` flag and `ReverifiedOn` field to track reverification status (via `BAOnboardingDetails` model).
    - Added `UserType` filter for agent reverification.
    - Added SND data fields (`SndCreatedBy`, `SndDistributor`, `SndDistributorId`) from `snd_user_detail_new` to `BAOnboardingDetails` model.
- **`performAdjudication`**:
    - Added `ReasonForApplicationStatusUpdate` field for adjudicator remarks/notes.
- **`performEdit`**:
    - Added `ReasonForStatusChange` field.
- **New APIs**:
    1. `/reverified` – List all reverified users.
    2. `/new` – List all new BAs.
    3. `/statistics/new-agents` – Get statistics for new agents.
    4. `/statistics/reverified-agents` – Get statistics for reverified agents.
    5. `/list/report` – List onboarded agents for reporting.

### Changed
- 
### Fixed
- 
### Deprecated
- 
### Security
- 