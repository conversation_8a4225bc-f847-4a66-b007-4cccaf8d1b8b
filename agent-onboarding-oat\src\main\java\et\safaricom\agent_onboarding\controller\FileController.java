package et.safaricom.agent_onboarding.controller;

import com.mpesautils.logmanager.LogManager;
import et.safaricom.agent_onboarding.dto.request.UploadFileByBase46RequestDto;
import et.safaricom.agent_onboarding.enums.FILE_GROUP;
import et.safaricom.agent_onboarding.enums.REQUIRED_FILE_TYPE;
import et.safaricom.agent_onboarding.service.FileService;
import et.safaricom.agent_onboarding.utils.ResponseTemplate;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

@RestController
@RequiredArgsConstructor
@RequestMapping("/api/v1/ba-onboarding/file")
@SecurityRequirement(name = "One Platform Token")
public class FileController {
    private final FileService fileService;

    @PostMapping("/upload")
    @Operation(summary = "Upload file", tags = "File handler", description = "An API for uploading a file")
    public ResponseTemplate<String> uploadFile(@RequestParam("id") Long id, @RequestParam("fileType") FILE_GROUP fileGroup, @RequestParam("file") MultipartFile file) {
        LogManager logManager = new LogManager();
        logManager.startClock();

        return fileService.saveFile(fileGroup, id, file, logManager);
    }

    @PostMapping("/upload/base64")
    @Operation(summary = "Upload file (base64 way)", tags = "File handler", description = "An API for uploading a file using base64")
    public ResponseTemplate<String> uploadFileByBase64(@RequestBody UploadFileByBase46RequestDto uploadFileByBase46RequestDto) {
        LogManager logManager = new LogManager();
        logManager.startClock();

        return fileService.saveFileFromBase64(uploadFileByBase46RequestDto, logManager);
    }

    @GetMapping("/download")
    @Operation(summary = "Download file", tags = "File handler", description = "An API for downloading a file")
    public ResponseEntity<?> getFile(@RequestParam("filePath") String filePath, @RequestParam("requiredFileType") REQUIRED_FILE_TYPE requiredFileType) {
        LogManager logManager = new LogManager();
        logManager.startClock();

        return fileService.getFile(filePath, requiredFileType, logManager);
    }
}
