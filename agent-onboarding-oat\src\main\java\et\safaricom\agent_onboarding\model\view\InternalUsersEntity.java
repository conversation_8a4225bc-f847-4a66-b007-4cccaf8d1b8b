package et.safaricom.agent_onboarding.model.view;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import et.safaricom.agent_onboarding.enums.UM_USER_STATUS;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.ToString;
import org.hibernate.annotations.Immutable;
import org.hibernate.annotations.Subselect;

import java.util.List;

@Getter
@ToString
@Entity
@Immutable
@JsonIgnoreProperties(ignoreUnknown = true)
@Subselect("""
    SELECT inu.id, inu.first_name, inu.middle_name, inu.last_name, inu.user_name, inu.parent_id, inu.contact_phone, inu.is_active, inu.status, inu.location_id, inu.user_hierarchy, inu.alternate_locations
    FROM usersandcluster.tbl_internal_user AS inu
""")
public class InternalUsersEntity {
    @Id
    @Column(name = "id")
    private Long id;

    @Column(name = "first_name")
    private String firstName;

    @Column(name = "middle_name")
    private String middleName;

    @Column(name = "last_name")
    private String lastName;

    @Column(name = "user_name")
    private String userName;

    @Column(name = "parent_id")
    private Long parentId;

    @Column(name = "contact_phone")
    private String contactPhone;

    @Column(name = "is_active")
    private Boolean isActive;

    @Column(name = "status")
    @Enumerated(EnumType.ORDINAL)
    private UM_USER_STATUS status;

    @Column(name = "location_id")
    private Long locationId;

    @Column(name = "user_hierarchy")
    private List<Long> userHierarchy;

    @Column(name = "alternate_locations")
    private List<Long> alternateLocations;

}