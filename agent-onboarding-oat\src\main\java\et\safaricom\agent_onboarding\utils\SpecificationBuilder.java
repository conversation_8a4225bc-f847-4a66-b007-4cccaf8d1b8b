package et.safaricom.agent_onboarding.utils;

import et.safaricom.agent_onboarding.enums.APPLICATION_STATUS;
import et.safaricom.agent_onboarding.enums.USER_STATUS;
import jakarta.persistence.criteria.*;
import org.springframework.data.jpa.domain.Specification;

import java.lang.reflect.Field;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class SpecificationBuilder<T> {
    public static <T> Specification<T> buildStoreSpecification(Object criteriaObject, Map<String, String> options) {
        return (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();
            LocalDateTime from = null;
            LocalDateTime to = null;

            List<String> rsmList = new ArrayList<>();
            List<String> ramList = new ArrayList<>();
            List<String> createdByList = new ArrayList<>();

            for (Field field : criteriaObject.getClass().getDeclaredFields()) {
                field.setAccessible(true);
                try {
                    Object value = field.get(criteriaObject);
                    if (value != null && !value.toString().isBlank()) {
                        String columnName = field.getName();
                        if (field.getType() == LocalDate.class) {
                            if (columnName.equalsIgnoreCase("startDate")) {
                                from = convertFromToSunRises(LocalDate.parse(String.valueOf(value)));
                            } else if (columnName.equalsIgnoreCase("endDate")) {
                                to = convertToToSunSets(LocalDate.parse(String.valueOf(value)));
                            }
                            continue;
                        }
                        if (columnName.equalsIgnoreCase("page") || columnName.equalsIgnoreCase("size")) {
                            continue;
                        }

                        if (columnName.equalsIgnoreCase("rsmList")) {
                            rsmList = (List<String>) value;
                            continue;
                        }

                        if (columnName.equalsIgnoreCase("ramList")) {
                            ramList = (List<String>) value;
                            continue;
                        }

                        if (columnName.equalsIgnoreCase("createdByList")) {
                            createdByList = (List<String>) value;
                            continue;
                        }

                        if (field.getType() == APPLICATION_STATUS.class) {
                            columnName = "status";
                        }

                        predicates.add(createPredicate(columnName, value.toString(), field.getType(), root, criteriaBuilder, options));
                    }
                    else {
                        if (field.getType() == APPLICATION_STATUS.class) {
                            predicates.add(criteriaBuilder.notEqual(root.get("status"), APPLICATION_STATUS.DRAFT));
                        }
                    }
                } catch (IllegalAccessException e) {
                    throw new RuntimeException(e);
                }
            }

            if (from != null && to != null) {
            predicates.add(criteriaBuilder.between(root.get("createdOn"), from, to));
        }

            if (!rsmList.isEmpty()) {
                Predicate rsmPredicate = root.get("rsm").in(rsmList);
                Predicate createdByPredicate = createdByList.isEmpty() ? null : root.get("createdBy").in(createdByList);

                if (rsmPredicate != null && createdByPredicate != null) {
                    predicates.add(criteriaBuilder.or(rsmPredicate, createdByPredicate));
                } else if (rsmPredicate != null) {
                    predicates.add(rsmPredicate);
                } else if (createdByPredicate != null) {
                    predicates.add(createdByPredicate);
                }
            }
            else if (!ramList.isEmpty()) {
                Predicate ramPredicate = root.get("ram").in(ramList);
                Predicate createdByPredicate = createdByList.isEmpty() ? null : root.get("createdBy").in(createdByList);

                if (ramPredicate != null && createdByPredicate != null) {
                    predicates.add(criteriaBuilder.or(ramPredicate, createdByPredicate));
                } else if (ramPredicate != null) {
                    predicates.add(ramPredicate);
                } else if (createdByPredicate != null) {
                    predicates.add(createdByPredicate);
                }
            } else if (!createdByList.isEmpty()) {
                Predicate createdByPredicate = root.get("createdBy").in(createdByList);
                predicates.add(createdByPredicate);
            }

            query.orderBy(criteriaBuilder.desc(root.get("createdOn")));
            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };
    }



//            // FIXED: Combined role list handling
//            List<Predicate> roleOrPredicates = new ArrayList<>();
//            if (!rsmList.isEmpty()) {
//                roleOrPredicates.add(root.get("rsm").in(rsmList));
//            }
//            if (!ramList.isEmpty()) {
//                roleOrPredicates.add(root.get("ram").in(ramList));
//            }
//            if (!createdByList.isEmpty()) {
//                roleOrPredicates.add(root.get("createdBy").in(createdByList));
//            }
//            if (!roleOrPredicates.isEmpty()) {
//
//                predicates.add(criteriaBuilder.or(roleOrPredicates.toArray(new
//                        Predicate[0])));
//            }
//
//            query.orderBy(criteriaBuilder.desc(root.get("createdOn")));
//            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
//        };
//    }
    private static <T> Predicate createPredicate(String columnName, String value, Class<?> fieldType, Root<T> root, CriteriaBuilder criteriaBuilder, Map<String, String> options) {
        Path<?> path = root.get(columnName);
        if (fieldType == String.class) {
            return criteriaBuilder.like(criteriaBuilder.lower((Expression<String>) path), "%" + value.toLowerCase() + "%");
        } else if (fieldType == Integer.class) {
            return criteriaBuilder.equal(path, Integer.parseInt(value));
        } else if (fieldType == Long.class) {
            return criteriaBuilder.equal(path, Long.parseLong(value));
        } else if (fieldType == Float.class) {
            return criteriaBuilder.equal(path, Float.parseFloat(value));
        } else if (fieldType == Double.class) {
            return criteriaBuilder.equal(path, Double.parseDouble(value));
        } else if (fieldType == Boolean.class) {
            return criteriaBuilder.equal(path, Boolean.parseBoolean(value));
        } else if (fieldType == APPLICATION_STATUS.class) {
            return criteriaBuilder.equal(path, APPLICATION_STATUS.valueOf(value));
        } else if (fieldType == USER_STATUS.class) {
            return criteriaBuilder.equal(path, USER_STATUS.valueOf(value));
        }
        return null;
    }

    public static LocalDateTime convertFromToSunRises(LocalDate date) {
        return date.atStartOfDay();
    }
    public static LocalDateTime convertToToSunSets(LocalDate date) {
        return date.atTime(23, 59, 59, 999999999);
    }
}

