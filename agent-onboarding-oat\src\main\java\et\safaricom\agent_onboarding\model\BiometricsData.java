package et.safaricom.agent_onboarding.model;

import et.safaricom.agent_onboarding.enums.BIOMETRICS_TYPE;
import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@Embeddable
@NoArgsConstructor
@AllArgsConstructor
public class BiometricsData {
    @Column(name = "biometrics_file_path")
    private String biometricsFilePath;

    @Enumerated(EnumType.STRING)
    @Column(name = "biometrics_type")
    private BIOMETRICS_TYPE biometricsType;

    @Column(name = "biometrics_sub_type")
    private String biometricsSubType;

}
