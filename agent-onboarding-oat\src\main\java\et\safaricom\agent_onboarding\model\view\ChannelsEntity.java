package et.safaricom.agent_onboarding.model.view;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import lombok.Getter;
import lombok.ToString;
import org.hibernate.annotations.Immutable;
import org.hibernate.annotations.Subselect;

import java.io.Serializable;

@Getter
@ToString
@Entity
@Immutable
@JsonIgnoreProperties(ignoreUnknown = true)
@Subselect("SELECT ch.id, ch.channel_name, ch.short_code, tl.id as location_id, tl.location_type, tlt.location_type_name, tl.location_name, ch.parent_id, tl.is_active FROM usersandcluster.tbl_channels ch " +
        "INNER JOIN usersandcluster.tbl_locations AS tl  ON tl.id = ch.location_id " +
        "INNER JOIN usersandcluster.tbl_location_type as tlt ON tlt.id = tl.location_type")
public class ChannelsEntity implements Serializable {
    @Id
    @Column(name = "id")
    private Long id;

    @Column(name = "channel_name")
    private String channelName;

    @Column(name = "short_code")
    private String shortCode;

    @Column(name = "location_id")
    private Long locationId;

    @Column(name = "location_type")
    private Long locationType;

    @Column(name = "location_type_name")
    private String locationTypeName;

    @Column(name = "location_name")
    private String locationName;

    @Column(name = "parent_id")
    private Long parentId;

    @Column(name = "is_active")
    private Boolean isActive;
}
