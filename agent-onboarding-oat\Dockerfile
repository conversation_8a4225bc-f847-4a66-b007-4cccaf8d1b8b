FROM et02-harbor.safaricomet.net/sma/eclipse-temurin:17-jre-alpine
#FROM eclipse-temurin:17-jre-alpine

# Timezone
ENV TZ=Africa/Nairobi
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# Rudementary
WORKDIR /app
# Application name same as deployment name
LABEL name="Ticketing Tool" \
authors="Zelalem Gizachew"
MAINTAINER "Zelalem Gizachew"

#Port
EXPOSE 8081


# Add App
ARG JAR_FILE=target/*.jar

COPY ${JAR_FILE} /app/service.jar

# Create the logs directory
RUN mkdir -p /logs
RUN mkdir -p /files
RUN mkdir -p /app/files

# User mode
RUN addgroup -S agent-onboarding-user && adduser -S agent-onboarding-user -G agent-onboarding-user

RUN chown -R agent-onboarding-user:agent-onboarding-user /app
RUN chown -R agent-onboarding-user:agent-onboarding-user /logs
RUN chown -R agent-onboarding-user:agent-onboarding-user /files
RUN chown -R agent-onboarding-user:agent-onboarding-user /app/files

USER root

ENTRYPOINT ["java","-jar","/app/service.jar"]
