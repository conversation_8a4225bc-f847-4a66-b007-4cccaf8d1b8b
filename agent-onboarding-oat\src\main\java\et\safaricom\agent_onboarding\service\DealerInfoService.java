package et.safaricom.agent_onboarding.service;

import et.safaricom.agent_onboarding.model.DealerInfo;
import et.safaricom.agent_onboarding.repository.DealerInfoRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class DealerInfoService {
    private final DealerInfoRepository dealerInfoRepository;

    public DealerInfo getById(Long id) {
        return dealerInfoRepository.findById(id).orElseThrow(() -> new RuntimeException("DealerInfo not found"));
    }
}
