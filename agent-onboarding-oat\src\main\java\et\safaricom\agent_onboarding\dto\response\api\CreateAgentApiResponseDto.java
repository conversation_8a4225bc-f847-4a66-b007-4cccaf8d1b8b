package et.safaricom.agent_onboarding.dto.response.api;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

import java.util.List;


@Data
@ToString
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class CreateAgentApiResponseDto {
    @JsonProperty("agentId")
    private String agentId;

    @JsonProperty("ekycId")
    private String ekycId;

    @JsonProperty("status")
    private Boolean status;

    @JsonProperty("recordStatus")
    private String recordStatus;

    @JsonProperty("description")
    private String description;

    @JsonProperty("roles")
    private List<String> roles;
}
