package et.safaricom.agent_onboarding.dto.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import et.safaricom.agent_onboarding.enums.FILE_GROUP;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

@Data
@ToString
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class UploadFileByBase46RequestDto {
    @JsonProperty(value = "id", required = true)
    @Schema(description = "The unique identifier for the file upload request.)")
    public Long id;

    @JsonProperty(value = "fileType", required = true)
    @Schema(description = "Type of file being uploaded. Possible values: " +
            "`FINGER_BIOMETRICS` (Fingerprint biometric data), " +
            "`AGENT_PHOTO` (Agent's photo), " +
            "`NID_CARD` (National ID card), " +
            "`NID_PHOTO` (Photo from National ID), " +
            "`LETTER_OF_CONSENT` (Signed consent letter).",
            example = "NID_PHOTO")
    public FILE_GROUP fileGroup;

    @JsonProperty(value = "fileBase64", required = true)
    @Schema(description = "Base64 encoded string of the file.", example = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...")
    public String fileBase64;

}
