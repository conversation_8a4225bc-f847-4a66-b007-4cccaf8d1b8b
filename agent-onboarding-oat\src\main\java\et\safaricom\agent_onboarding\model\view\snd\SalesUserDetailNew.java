package et.safaricom.agent_onboarding.model.view.snd;



import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import lombok.Getter;
import lombok.ToString;
import org.hibernate.annotations.Immutable;
import org.hibernate.annotations.Subselect;


import java.time.LocalDate;
import java.time.LocalDateTime;


@Getter
@ToString
@Entity
@Immutable

@JsonIgnoreProperties(ignoreUnknown = true)

@Subselect("""
    SELECT   user_id, name, contact_number, nid_waiting_card_number, nid_number, status_changed_by, approved_by, commission_number, status_ch_by_name,
        status_ch_by_type, status_change_date, remarks, onboarding_date, user_status, user_type, onboarded_by_name, created_by, has_sales_channel,
        sales_channel_name, agent_division, agent_region, agent_cluster, distributor, distributor_id, has_ekyc_role, assigned_asm
    FROM onboardings.snd_user_detail_new
""")

public class SalesUserDetailNew{
    @Id
    @Column(name = "user_id")
    private String userId;

    @Column(name = "name")
    private String name;

    @Column(name = "contact_number")
    private String contactNumber;

    @Column(name = "nid_waiting_card_number")
    private String nidWaitingCardNumber;

    @Column(name = "nid_number")
    private String nidNumber;

    @Column(name = "status_changed_by")
    private String statusChangedBy;

    @Column(name = "approved_by")
    private String approvedBy;

    @Column(name = "commission_number")
    private String commissionNumber;

    @Column(name = "status_ch_by_name")
    private String statusChangedByName;

    @Column(name = "status_ch_by_type")
    private String statusChangedByType;

    @Column(name = "status_change_date")
    private LocalDateTime statusChangeDate;

    @Column(name = "remarks")
    private String remarks;

    @Column(name = "onboarding_date")
    private LocalDate onboardingDate;

    @Column(name = "user_status")
    private String userStatus;

    @Column(name = "user_type")
    private String userType;

    @Column(name = "onboarded_by_name")
    private String onboardedByName;

    @Column(name = "created_by")
    private String createdBy;

    @Column(name = "has_sales_channel")
    private Boolean hasSalesChannel;

    @Column(name = "sales_channel_name")
    private String salesChannelName;

    @Column(name = "agent_division")
    private String agentDivision;

    @Column(name = "agent_region")
    private String agentRegion;

    @Column(name = "agent_cluster")
    private String agentCluster;

    @Column(name = "distributor")
    private String distributor;

    @Column(name = "distributor_id")
    private String distributorId;

    @Column(name = "has_ekyc_role")
    private Boolean hasEkycRole;

    @Column(name = "assigned_asm")
    private String assignedAsm;


}