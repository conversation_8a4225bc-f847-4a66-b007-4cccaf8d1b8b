package et.safaricom.agent_onboarding.service;

import com.mpesautils.logmanager.Log;
import com.mpesautils.logmanager.LogManager;
import et.safaricom.agent_onboarding.dto.response.ChannelsResponseDto;
import et.safaricom.agent_onboarding.dto.response.query.UserDropDownQueryResponseDto;
import et.safaricom.agent_onboarding.enums.DROPDOWN_USER_ROLE;
import et.safaricom.agent_onboarding.enums.UM_USER_STATUS;
import et.safaricom.agent_onboarding.geo.repository.ClustersLDBRepository;
import et.safaricom.agent_onboarding.geo.repository.MasterLocationRepository;
import et.safaricom.agent_onboarding.model.RequiredInformationRemark;
import et.safaricom.agent_onboarding.model.view.ChannelsEntity;
import et.safaricom.agent_onboarding.repository.RequiredInformationRemarkRepository;
import et.safaricom.agent_onboarding.repository.view.ChannelsEntityRepository;
import et.safaricom.agent_onboarding.repository.view.InternalUsersEntityRepository;
import et.safaricom.agent_onboarding.utils.Constants;
import et.safaricom.agent_onboarding.utils.Helper;
import et.safaricom.agent_onboarding.utils.LoggerCommon;
import et.safaricom.agent_onboarding.utils.ResponseTemplate;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class OtherService {
    private final LoggerCommon loggerCommon;

    private final BAOnboardingService baOnboardingService;

    private final InternalUsersEntityRepository internalUsersEntityRepository;
    private final ChannelsEntityRepository channelsEntityRepository;
    private final MasterLocationRepository masterLocationRepository;
    private final ClustersLDBRepository clustersLDBRepository;
    private final RequiredInformationRemarkRepository requiredInformationRemarkRepository;


    public ResponseTemplate<?> getUsers(Boolean isLocationSpecific, DROPDOWN_USER_ROLE userRole, LogManager logManager) {
        String transactionId = "ONE_PLATFORM_BA_ONBOARDING_" + System.currentTimeMillis();

        Log log = new Log(transactionId, "info", "List Distribution RSM/RAM", "", "", "", "ONE_PLATFORM_BA_ONBOARDING", "ONE_PLATFORM_USER_MANAGEMENT_DB",
                "0", "Default!", "Default Detailed Message.", "");
        try {
            if (isLocationSpecific == null || !isLocationSpecific) { // Todo: Filter by the location of the initiator
                List<UserDropDownQueryResponseDto> activeUsersQueryResponseDtoList = new ArrayList<>();
                switch (userRole) {
                    case RSM -> {
                        activeUsersQueryResponseDtoList =
                                internalUsersEntityRepository.findAllActiveUsersByRoleId(Constants.DISTRIBUTION_RSM_ROLE_ID)
                                        .stream()
                                        .filter(u -> u.getIsActive() && UM_USER_STATUS.values()[u.getStatus()].equals(UM_USER_STATUS.ACTIVE))
                                        .map(rsm -> UserDropDownQueryResponseDto.builder()
                                                    .username(rsm.getUsername())
                                                    .fullName(Helper.formatFullName(rsm.getFirstName(), rsm.getMiddleName(), rsm.getLastName()))
                                                .build()
                                        )
                                        .collect(Collectors.toList());
                    }
                    case RAM -> {
                        activeUsersQueryResponseDtoList = internalUsersEntityRepository.findAllActiveUsersByRoleId(Constants.RAM_ROLE_ID)
                                .stream()
                                .filter(u -> u.getIsActive() && UM_USER_STATUS.values()[u.getStatus()].equals(UM_USER_STATUS.ACTIVE))
                                .map(rsm -> UserDropDownQueryResponseDto.builder()
                                            .username(rsm.getUsername())
                                            .fullName(Helper.formatFullName(rsm.getFirstName(), rsm.getMiddleName(), rsm.getLastName()))
                                        .build()
                                )
                                .collect(Collectors.toList());
                    }
                }

//                activeUsersQueryResponseDtoList.forEach(rsm -> rsm.setFullName(Helper.formatFullName(rsm.getFirstName(), rsm.getMiddleName(), rsm.getLastName())));
                logManager.info(loggerCommon.getLog(log, "info", 0, "Success!", "ACTIVE Distribution [ " + userRole.name() + " ] Users Size: [ " + activeUsersQueryResponseDtoList.size() + " ]"));
                return ResponseTemplate.success(activeUsersQueryResponseDtoList);
            }
            return ResponseTemplate.success(new ArrayList<>());
        } catch (Exception e) {
            e.printStackTrace();
            logManager.error(loggerCommon.getLog(log, 1, "Something went wrong! Please try again.", e));
            return ResponseTemplate.error("Something went wrong! Please try again.");
        }
    }

    public ResponseTemplate<?> getChannels(Boolean isLocationSpecific, Long distributorId, LogManager logManager) {
        String transactionId = "ONE_PLATFORM_BA_ONBOARDING_" + System.currentTimeMillis();

        Log log = new Log(transactionId, "info", "List Channels", "", "", "", "ONE_PLATFORM_BA_ONBOARDING", "ONE_PLATFORM_USER_MANAGEMENT_DB",
                "0", "Default!", "Default Detailed Message.", "");

        try {
            if (isLocationSpecific == null || !isLocationSpecific) { // Todo: Filter by the location of the initiator
                if (distributorId == null || distributorId.equals(0L)) {
                    return ResponseTemplate.success(
                            channelsEntityRepository.findAllDistributors().stream()
                                    .filter(ChannelsEntity::getIsActive)
                                    .map(ch -> ChannelsResponseDto.builder()
                                                .channelId(ch.getId())
                                                .channelName(ch.getChannelName())
                                            .build()
                                    )
                                    .toList()
                    );
                } else if ((distributorId >= 1L && distributorId <= 23L) || distributorId == 582L) {
                    return ResponseTemplate.success(channelsEntityRepository.findAllShops(distributorId).stream().filter(ChannelsEntity::getIsActive).map(ch -> ChannelsResponseDto.builder().channelId(ch.getId()).channelName(ch.getChannelName()).build()).toList());
                }
            }
            return ResponseTemplate.error("No active distributor shop found by the specified distributor.");
        } catch (Exception e) {
            e.printStackTrace();
            logManager.error(loggerCommon.getLog(log, 1, "Something went wrong! Please try again.", e));
            return ResponseTemplate.error("Something went wrong! Please try again.");
        }

    }

    public ResponseTemplate<?> getGeoLocations(String division, String region, String cluster, LogManager logManager) {
        String transactionId = "ONE_PLATFORM_BA_ONBOARDING_" + System.currentTimeMillis();

        Log log = new Log(transactionId, "info", "List Geo Locations", "", "", "", "ONE_PLATFORM_BA_ONBOARDING", "ONE_PLATFORM_GEOSPATIAL_DB",
                "0", "Default!", "Default Detailed Message.", "");

        try {
            if (cluster != null && !cluster.isBlank()) {
                Set<String> sites = masterLocationRepository.findAllSiteIdByDistClusterName(cluster);
                if (sites != null && !sites.isEmpty()) {
                    List<String> siteList = new ArrayList<>();
                    siteList.add("All");
                    siteList.addAll(sites);
                    return ResponseTemplate.success(siteList);
                } else {
                    logManager.info(loggerCommon.getLog(log, "info", 1, "No sites found for the specified cluster.", "Cluster [ " + cluster + " ]."));
                    return ResponseTemplate.error("No sites found for the specified cluster.");
                }
            } else if (region != null && !region.isBlank()) {
                Set<String> clusters = clustersLDBRepository.findAllClustersByRegion(region);
                if (clusters != null && !clusters.isEmpty()) {
                    List<String> clusterList = new ArrayList<>();
                    clusterList.add("All");
                    clusterList.addAll(clusters);
                    return ResponseTemplate.success(clusterList);
                } else {
                    logManager.info(loggerCommon.getLog(log, "info", 1, "No clusters found for the specified region.", "Region [ " + region + " ]."));
                    return ResponseTemplate.error("No clusters found for the specified region.");
                }
            } else if (division != null && !division.isBlank()) {
                Set<String> regions = clustersLDBRepository.findAllRegionsByDivision(division);
                if (regions != null && !regions.isEmpty()) {
                    List<String> regionsList = new ArrayList<>(regions.stream().toList());
                    regionsList.add("All");
                    return ResponseTemplate.success(regionsList);
                } else {
                    logManager.info(loggerCommon.getLog(log, "info", 1, "No regions found for the specified division.", "Division [ " + division + " ]."));
                    return ResponseTemplate.error("No regions found for the specified division.");
                }
            } else {
                Set<String> divisions = clustersLDBRepository.findAllDivisions();
                if (divisions != null && !divisions.isEmpty()) {
                    List<String> divisionsList = new ArrayList<>(divisions.stream().toList());
                    divisionsList.add("All");
                    return ResponseTemplate.success(divisionsList);
                } else {
                    logManager.info(loggerCommon.getLog(log, "info", 1, "No divisions found.", null));
                    return ResponseTemplate.error("No divisions found.");
                }
            }
        }
        catch (Exception e) {
            e.printStackTrace();
            logManager.error(loggerCommon.getLog(log, 1, "Something went wrong! Please try again.", e));
            return ResponseTemplate.error("Something went wrong! Please try again.");
        }
    }

    public ResponseTemplate<?> getBuiltInRemarks(LogManager logManager) {
        String transactionId = "ONE_PLATFORM_BA_ONBOARDING_" + System.currentTimeMillis();

        Log log = new Log(transactionId, "info", "List Pre-Defined Remarks", "", "", "", "ONE_PLATFORM_BA_ONBOARDING", "ONE_PLATFORM_BA_ONBOARDING",
                "0", "Default!", "Default Detailed Message.", "");

        try {
            List<RequiredInformationRemark> requiredInformationRemarks = requiredInformationRemarkRepository.findAllByIsActive(true);
            if (requiredInformationRemarks.isEmpty()) {
                logManager.info(loggerCommon.getLog(log, "info", 1, "No pre-defined remarks were found!", null));
                return ResponseTemplate.error("No pre-defined remarks were found!");
            }

            return ResponseTemplate.success(
                    requiredInformationRemarks.stream().map(RequiredInformationRemark::getRemark).toList()
            );
        }
        catch (Exception e) {
            e.printStackTrace();
            logManager.error(loggerCommon.getLog(log, 1, "Something went wrong! Please try again.", e));
            return ResponseTemplate.error("Something went wrong! Please try again.");
        }
    }

    public ResponseTemplate<?> sendKafkaMessageTest(Long id, LogManager logManager) {
        String transactionId = "ONE_PLATFORM_BA_ONBOARDING_" + System.currentTimeMillis();

        Log log = new Log(transactionId, "info", "Testing Kafka", "", "", "", "ONE_PLATFORM_BA_ONBOARDING", "ONE_PLATFORM_BA_ONBOARDING",
                "0", "Default!", "Default Detailed Message.", "");
        return baOnboardingService.triggerSendKafkaMessageTest(id, logManager, log);
    }
}
