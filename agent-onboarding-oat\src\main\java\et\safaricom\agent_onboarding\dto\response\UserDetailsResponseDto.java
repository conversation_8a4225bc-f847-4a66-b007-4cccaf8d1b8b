package et.safaricom.agent_onboarding.dto.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;

import java.util.List;

@Data
@ToString
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class UserDetailsResponseDto {
    @JsonProperty("id")
    private Long id;

    @JsonProperty("firstName")
    private String firstName;

    @JsonProperty("middleName")
    private String middleName;

    @JsonProperty("lastName")
    private String lastName;

    @JsonProperty("contactPhone")
    private String contactPhone;

    @JsonProperty("locationId")
    private Long locationId;

    @JsonProperty("channelId")
    private Long channelId;

    @JsonProperty("userHierarchies")
    private List<Long> userHierarchies;

    @JsonProperty("alternateLocations")
    private List<Long> alternateLocations;

}
