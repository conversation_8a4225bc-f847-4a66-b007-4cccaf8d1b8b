# Todo: Update the base urls for production to production on prod
application.name= agent-onboarding
spring.application.name= ${application.name}

#Deploy
server.port= 8090

#spring.profiles.active=${ACTIVE_PROFILE}
spring.profiles.active= oat
spring.jpa.hibernate.ddl-auto= update

ba-onboarding.file-upload.dir=files

spring.servlet.multipart.max-file-size=10MB

sms.command-id=sendsms
sms.message-type=SMS
sms.sender-name=Partner Hub
sms.ba-onboarding.application-status-update.template= Your application for {0} is {1}. Please check for details on CTApp. Thank you.
sms.ba-onboarding.agent-application-status-approved.template= Dear {0},{1} Your registration is successful. Your User ID is {2}. Log in to EKYC to get started. For support, contact 400.
sms.ba-onboarding.notify-adjudicator.template= Dear {0}, You have received a new BA application request ({1}) from {2}. Please log in to One Platform and continue with the adjudication process.


sms.reverified.ba-onboarding.application-status-update.template= Your reverification application for {0} is {1}. Please check for details on CTApp. Thank you.
sms.reverified.ba-onboarding.agent-application-status-approved.template= Dear {0},{1} Your reverification registration is successful. Your User ID is {2}. Log in to EKYC to get started. For support, contact 400.
sms.reverified.ba-onboarding.notify-adjudicator.template= Dear {0}, You have received a new BA reverification application request ({1}) from {2}. Please log in to One Platform and continue with the adjudication process.


dxl.add-ekyc-agent.command-id=AddEKYCAgent
dxl.add-ekyc-agent.source-system=E-KYC APP
dxl.add-ekyc-agent.version=v1.2

# Kafka Prod Configs (No UAT/OAT/BP)
spring.kafka.bootstrap-servers=10.4.42.65:9094,10.4.42.68:9094,10.4.42.59:9094
#spring.kafka.bootstrap-servers=10.4.142.61:9094,10.4.42.48:9094,10.4.42.45:9094
spring.kafka.template.default-topic=Agent-Registration-Details
spring.kafka.producer.key-serializer=org.apache.kafka.common.serialization.StringSerializer
spring.kafka.producer.value-serializer=org.apache.kafka.common.serialization.StringSerializer
spring.kafka.properties.security.protocol=SASL_PLAINTEXT
spring.kafka.properties.sasl.mechanism=SCRAM-SHA-512
spring.kafka.properties.sasl.jaas.config=org.apache.kafka.common.security.scram.ScramLoginModule required username="mpesa-dxl-mdc1" password="jTP0vy1xeveT";

#  Logging configuration
logging.level.root= INFO
logging.file.path= ${user.dir}/logs
logging.file.name= ${application.name}.log


