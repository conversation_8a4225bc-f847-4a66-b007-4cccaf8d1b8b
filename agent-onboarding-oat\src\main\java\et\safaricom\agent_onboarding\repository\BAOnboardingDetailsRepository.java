package et.safaricom.agent_onboarding.repository;

import et.safaricom.agent_onboarding.enums.APPLICATION_STATUS;
import et.safaricom.agent_onboarding.model.BAOnboardingDetails;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import org.springframework.data.domain.Pageable;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Repository
public interface BAOnboardingDetailsRepository extends CustomBAOnboardingDetailsRepository, JpaRepository<BAOnboardingDetails, Long>, JpaSpecificationExecutor<BAOnboardingDetails> {
    Optional<BAOnboardingDetails> findByMsisdnOrCrmBiometricsId(String msisdn, String biometricsId);

    Optional<BAOnboardingDetails> findByMsisdnOrCrmBiometricsIdOrAgentId(String msisdn, String biometricsId, String agentId);

    Optional<BAOnboardingDetails> findBySessionId(UUID sessionId);

    Optional<BAOnboardingDetails> findByAgentId(String agentId);
    List<BAOnboardingDetails> findAllByAgentId(String agentId);

    

    Optional<BAOnboardingDetails> findByNidSubjectId(String subject);

    @Query("SELECT ba FROM BAOnboardingDetails ba JOIN ba.biometricsIdList bio WHERE bio IN :biometricsIdList")
    List<BAOnboardingDetails> findAllByBiometricsIdList(@Param("biometricsIdList") List<String> biometricsIdList);

    @Query("SELECT COUNT(ba.id) FROM BAOnboardingDetails AS ba WHERE ba.status = :status")
    Integer countAllByStatus(@Param("status") APPLICATION_STATUS status);

    @Query("SELECT COUNT(ba.id) FROM BAOnboardingDetails AS ba WHERE ba.status = :status AND (ba.rsm = :rsm OR ba.ram = :ram)")
    Integer countAllByStatusAndUser(@Param("status") APPLICATION_STATUS status, @Param("rsm") String rsm, @Param("ram") String ram);

    @Query("SELECT COUNT(ba.id) FROM BAOnboardingDetails AS ba WHERE ba.status != :status AND ba.status != :except")
    Integer  countAllByStatusInverseWithException(@Param("status") APPLICATION_STATUS status, @Param("except") APPLICATION_STATUS exceptStatus);

    @Query("SELECT ba.division AS location, COUNT(ba.id) AS totalUsers FROM BAOnboardingDetails AS ba WHERE ba.status = :status GROUP BY ba.division")
    List<CountByLocationDto> countAllByDivision(@Param("status") APPLICATION_STATUS status);

    @Query("SELECT ba.region AS location, COUNT(ba.id) AS totalUsers FROM BAOnboardingDetails AS ba WHERE ba.status = :status GROUP BY ba.region")
    List<CountByLocationDto> countAllByRegion(@Param("status") APPLICATION_STATUS status);

    @Query("SELECT ba.cluster AS location, COUNT(ba.id) AS totalUsers FROM BAOnboardingDetails AS ba WHERE ba.status = :status GROUP BY ba.cluster")
    List<CountByLocationDto> countAllByCluster(@Param("status") APPLICATION_STATUS status);

    @Query("SELECT COUNT(ba.id) FROM BAOnboardingDetails AS ba WHERE ba.status != :status AND ba.status != :except AND ba.createdOn BETWEEN :from AND :to")
    Integer countAllByStatusInverseWithExceptionAndLast30Days(@Param("status") APPLICATION_STATUS status, @Param("except") APPLICATION_STATUS exceptStatus, @Param("from") LocalDateTime from, @Param("to") LocalDateTime to);

    @Query("SELECT COUNT(ba.id) FROM BAOnboardingDetails AS ba WHERE ba.status = :status AND (ba.distributorId = :distributorId OR ba.distributorShopId = :distributorShopId)")
    Integer countAllByStatusAndDistributor(@Param("status") APPLICATION_STATUS status, @Param("distributorId") Long distributorId, @Param("distributorShopId") Long distributorShopId);

    interface CountByLocationDto {
        String getLocation();
        Integer getTotalUsers();
    }

    @Query("SELECT ba FROM BAOnboardingDetails ba WHERE ba.isReverified = true")
    List<BAOnboardingDetails> findAllReverifiedBAs();
    @Query("SELECT ba FROM BAOnboardingDetails ba WHERE (ba.isReverified IS NULL OR ba.isReverified = false)")
    List<BAOnboardingDetails> findAllNewBAs();

    int countByIsReverified(boolean isReverified);
    int countByIsReverifiedAndStatus(boolean isReverified, APPLICATION_STATUS status);

    //For new agents statistics (isReverified = false)
    @Query("SELECT COUNT(b) FROM BAOnboardingDetails b WHERE b.isReverified = false AND b.status <> 'DRAFT'")
    int countByIsReverifiedFalse();

    @Query("SELECT COUNT(b) FROM BAOnboardingDetails b WHERE b.isReverified = false AND b.status = 'APPROVED'")
    int countByIsReverifiedFalseAndStatusApproved();

    @Query("SELECT COUNT(b) FROM BAOnboardingDetails b WHERE b.isReverified = false AND b.status = 'PENDING'")
    int countByIsReverifiedFalseAndStatusPending();

    @Query("SELECT COUNT(b) FROM BAOnboardingDetails b WHERE b.isReverified = false AND b.status = 'REJECTED'")
    int countByIsReverifiedFalseAndStatusRejected();

    // For reverified agents statistics (isReverified = true)
    @Query("SELECT COUNT(b) FROM BAOnboardingDetails b WHERE b.isReverified = true AND b.status <> 'DRAFT'")
    int countByIsReverifiedTrue();

    @Query("SELECT COUNT(b) FROM BAOnboardingDetails b WHERE b.isReverified = true AND b.status = 'APPROVED'")
    int countByIsReverifiedTrueAndStatusApproved();

    @Query("SELECT COUNT(b) FROM BAOnboardingDetails b WHERE b.isReverified = true AND b.status = 'PENDING'")
    int countByIsReverifiedTrueAndStatusPending();

    @Query("SELECT COUNT(b) FROM BAOnboardingDetails b WHERE b.isReverified = true AND b.status = 'REJECTED'")
    int countByIsReverifiedTrueAndStatusRejected();

    @Query("SELECT ba FROM BAOnboardingDetails ba WHERE ba.isReverified = true")
    Page<BAOnboardingDetails> findAllReverified(Pageable pageable);

}
