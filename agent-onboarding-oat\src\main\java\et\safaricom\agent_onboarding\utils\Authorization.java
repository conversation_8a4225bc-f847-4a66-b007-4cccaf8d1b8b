package et.safaricom.agent_onboarding.utils;
import org.springframework.stereotype.Component;

/**
 * The Authorization class manages role-based access control for various functionalities.
 * It defines which role IDs are authorized to perform specific actions.
 * Role IDs and their descriptions:
 * <ul>
 *     <li>3  - Distribution TDR Supervisor</li>
 *     <li>6  - Distribution TDR</li>
 *     <li>7  - Merchant Recruitment TDR [ TMR ]</li>
 *     <li>8  - Distributor Admin</li>
 *     <li>21 - Fraud Manager</li>
 *     <li>30 - Distribution NSM</li>
 *     <li>31 - Merchant NSM</li>
 *     <li>32 - One NSM</li>
 *     <li>33 - Distribution RSM</li>
 *     <li>34 - Merchant RSM</li>
 *     <li>35 - One RSM</li>
 *     <li>39 - Distribution Shop Manager</li>
 *     <li>40 - One Platform Admin</li>
 *     <li>94 - EKYC Support</li>
 *     <li>95 - TAR</li>
 * </ul>
 */
@Component
public class Authorization {
    public static final Long[] CAN_ONBOARD_APP = {1L, 8L, 30L, 33L, 39L, 95L};
    public static final Long[] CAN_ONBOARD_WEB = {1L, 8L, 30L, 33L, 39L, 40L, 94L, 95L};
    public static final Long[] CAN_EDIT_EX_PD_STA_APP = {1L, 8L, 30L, 33L, 39L, 95L};
    public static final Long[] CAN_EDIT_EX_PD_STA_WEB = {1L, 8L, 30L, 33L, 39L, 40L, 94L, 95L};
    public static final Long[] CAN_EDIT_PD_WEB = {1L, 30L, 33L, 40L, 94L};
    public static final Long[] CAN_ADJUDICATE_WEB = {1L, 30L, 33L, 40L, 94L};
    public static final Long[] CAN_VIEW_ALL_STATUS_USERS_WEB = {1L, 8L, 19L, 21L, 30L, 33L, 39L, 40L, 94L, 95L};
    public static final Long[] CAN_VIEW_ALL_STATUS_USERS_APP = {1L, 8L, 30L, 33L, 39L, 95L};
    public static final Long[] CAN_VIEW_CREATED_USERS_LIST_APP = {1L, 8L, 30L, 33L, 39L, 95L};
    public static final Long[] CAN_EDIT_USERS_STATUS_WEB = {1L, 8L, 19L, 30L, 33L, 95L};
    public static final Long[] CAN_VIEW_REPORT_AND_DASHBOARD_WEB = {1L, 8L, 19L, 21L, 30L, 33L, 39L, 40L, 94L, 95L};
}
