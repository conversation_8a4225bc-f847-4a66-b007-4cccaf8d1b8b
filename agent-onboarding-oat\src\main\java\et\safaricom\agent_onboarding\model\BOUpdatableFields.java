package et.safaricom.agent_onboarding.model;

import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@Builder
@Embeddable
@NoArgsConstructor
@AllArgsConstructor
public class BOUpdatableFields {
    @Column(name = "updatable_fields")
    private String updatableFields;

    @Column(name = "requested_by")
    private String requestedBy;

    @Column(name = "requested_on")
    private LocalDateTime requestedOn;
}
