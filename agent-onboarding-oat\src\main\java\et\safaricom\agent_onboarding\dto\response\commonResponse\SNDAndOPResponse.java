package et.safaricom.agent_onboarding.dto.response.commonResponse;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.time.LocalDateTime;
import java.util.List;

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class SNDAndOPResponse {
    private int totalPages;
    private int page;
    private int pageSize;
    private List<UserResult> results;

    @Data
    @SuperBuilder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class UserResult {
        private String userId;
        private String userType;
        private List<String> userRole;
        private String contactNo;
        private String emailId;
        private String status;
        private String firstName;
        private String lastName;

        private String parentUserId;            // new
        private String contractStartDate;
        private String contractEndDate;
        private String ekycBiometricId;
        private String dealerId;
        private String channelType;
        private String channelSubType;
        private String salesChannelName;

        private String region;                  // new (top-level region)
        @JsonProperty("sales_area")
        private String salesArea;               // new (maps sales_area)
        private String cluster;                 // new
        private String route;                   // new (nullable in JSON)

        private String location;
        private String wallet;
        private List<Address> addresses;

        private String salesChannelStatus;
        private String rootParentAccManagerId;
        private String rootParentAccManagerName;
        private String rootParentId;
        private String rootParentName;

        private String parentSalesChannelName;  // new
        private Integer parentSalesChannelId;   // new
        private String parentAssignedAsm;       // new
    }

    @Data
    @SuperBuilder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Address {
        private String addressType;
        private String woreda;
        private String subCity;
        private String region;
        private String zone;        // new
        private String location;
    }
}
