package et.safaricom.agent_onboarding.service;

import com.mpesautils.logmanager.Log;
import com.mpesautils.logmanager.LogManager;
import et.safaricom.agent_onboarding.dto.request.api.*;
import et.safaricom.agent_onboarding.dto.response.api.CRMCustomerInfoApiResponseDto;
import et.safaricom.agent_onboarding.dto.response.api.CreateAgentApiResponseDto;
import et.safaricom.agent_onboarding.dto.response.api.EKycVerificationApiResponseDto;
import et.safaricom.agent_onboarding.dto.response.api.EKycVerificationIdApiResponseDto;
import et.safaricom.agent_onboarding.enums.EKYC_DEDUB_TYPE;
import et.safaricom.agent_onboarding.utils.LoggerCommon;
import et.safaricom.agent_onboarding.utils.ResponseTemplate;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class APIGatewayService {

    private final LoggerCommon loggerCommon;

    @Value("${ekyc.auth.username}")
    private String eKycAuthUsername;

    @Value("${ekyc.auth.password}")
    private String eKycAuthPassword;

    @Value("${ekyc.biometrics-verification.finger-type}")
    private String eKycFingerType;

    @Value("${ekyc.create-agent.role}")
    private String eKycAgentEKycRole;

    @Value("${ekyc.create-agent.mpesa-role}")
    private String eKycAgentMPESAEKycRole;

    @Value("${crm.auth.username}")
    private String crmAuthUsername;

    @Value("${crm.auth.password}")
    private String crmAuthPassword;

    @Value("${crm.x-correlation-conversationid}")
    private String crmConversationId;

    @Value("${crm.x-source-system}")
    private String crmSourceSystem;

    @Value("${crm.x-source-identity-token}")
    private String crmSourceIdentityToken;

    @Value("${x-route-id}")
    private String crmRouteId;

    @Value("${x-correlation-id}")
    private String crmCorrelationId;

    @Value("${ekyc.auth.url}")
    private String eKycAuthUrl;

    @Value("${ekyc.verification.url}")
    private String eKycVerificationUrl;

    @Value("${ekyc.verification-dedup.url}")
    private String eKycDeDupVerificationUrl;

    @Value("${ekyc.vetting.url}")
    private String eKycVettingUrl;

    @Value("${ekyc.create-agent.url}")
    private String eKycCreateAgentUrl;

    @Value("${dxl.add-ekyc-agent.command-id}")
    private String dxlEycAddAgentCommandId;

    @Value("${dxl.add-ekyc-agent.source-system}")
    private String dxlEycAddAgentSourceSystem;

    @Value("${dxl.add-ekyc-agent.version}")
    private String dxlEycAddAgentVersion;

    @Value("${dxl.add-ekyc-agent.url}")
    private String dxlEycAddAgentUrl;

    @Value("${crm-auth.url}")
    private String crmAuthUrl;

    @Value("${crm.customer-info.url}")
    private String crmCustomerInfoUrl;

    @Value("${sms.username}")
    private String smsUserName;

    @Value("${sms.password}")
    private String smsPassword;

    @Value("${sms.command-id}")
    private String smsCommandId;

    @Value("${sms.sender-name}")
    private String smsSenderName;

    @Value("${sms.message-type}")
    private String smsMessageType;

    @Value("${dxl.send-sms.url}")
    private String dxlSendSMSUrl;


    private ResponseEntity<Map<String, String>> eKycAuth(LogManager logManager, Log log) {
        try {
            RestTemplate restTemplate = new RestTemplate();
            HttpEntity<Map<String, String>> httpEntity = new HttpEntity<>(Map.of(
                    "username", eKycAuthUsername,
                    "password", eKycAuthPassword
            ), new HttpHeaders());

            ResponseEntity<Map<String, String>> response = restTemplate.exchange(eKycAuthUrl, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<>() {
            });

            logManager.info(loggerCommon.getLog(log, "info", 0, "eKyc Authentication", response.getBody()));

            return response;
        }
        catch (Exception e) {
            e.printStackTrace();
            logManager.error(loggerCommon.getLog(log, "error", 1, "Something went wrong on eKYC Authentication.", e.getMessage()));
            return ResponseEntity.internalServerError().body(Map.of("message", "Something went wrong on eKYC Authentication!"));
        }
    }

    public ResponseTemplate<List<String>> eKycDeDupHandler(EKycVerificationApiRequestDto request, String biometricsId, EKYC_DEDUB_TYPE eKycDeDubType, LogManager logManager, Log log) {
        switch (eKycDeDubType) {
            case BY_BIOMETRICS -> {
                return eKycBiometricDeDup(request, logManager, log);
            }
            case BY_BIOMETRICS_ID -> {
                return eKycBiometricIdDeDup(biometricsId, logManager, log);
            }
        }

        return ResponseTemplate.error("Bad Request!");
    }

    public ResponseTemplate<List<String>> eKycBiometricDeDup(EKycVerificationApiRequestDto request, LogManager logManager, Log log) {
        log.setTargetSystem("EKYC");
        try {
            RestTemplate restTemplate = new RestTemplate();
            ResponseEntity<Map<String, String>> eKycAuthResponse = eKycAuth(logManager, log);
            if (!eKycAuthResponse.getStatusCode().is2xxSuccessful()) {
                return ResponseTemplate.error(eKycAuthResponse.getBody().get("message"));
            }

            String token = eKycAuthResponse.getBody().get("access_token");

            HttpHeaders headers = new HttpHeaders();
            headers.setBearerAuth(token);

            request.getBiometricData().get(0).setBiometricSubType(eKycFingerType);

            HttpEntity<?> httpEntity = new HttpEntity<>(request, headers);

            ResponseEntity<EKycVerificationApiResponseDto> eKycVerificationApiResponseDtoResponseEntity = restTemplate.exchange(eKycDeDupVerificationUrl, HttpMethod.POST, httpEntity, EKycVerificationApiResponseDto.class);

            return getResponseTemplate(logManager, log, eKycVerificationApiResponseDtoResponseEntity, loggerCommon);
        }
        catch (Exception e) {
            e.printStackTrace();
            logManager.error(loggerCommon.getLog(log, "error", 1, "Something went wrong on eKYC DeDup By Biometrics.", e.getMessage()));
            return ResponseTemplate.error("Something went wrong! Please try again.");
        }
    }

    public ResponseTemplate<List<String>> eKycBiometricIdDeDup(String biometricsId, LogManager logManager, Log log) {
        log.setTargetSystem("EKYC");
        try {
            RestTemplate restTemplate = new RestTemplate();
            ResponseEntity<Map<String, String>> eKycAuthResponse = eKycAuth(logManager, log);
            if (!eKycAuthResponse.getStatusCode().is2xxSuccessful()) {
                return ResponseTemplate.error(eKycAuthResponse.getBody().get("message"));
            }

            String token = eKycAuthResponse.getBody().get("access_token");

            HttpHeaders headers = new HttpHeaders();
            headers.setBearerAuth(token);

            HttpEntity<?> httpEntity = new HttpEntity<>(headers);

            ResponseEntity<EKycVerificationApiResponseDto> eKycVerificationApiResponseDtoResponseEntity = restTemplate.exchange(eKycDeDupVerificationUrl + "/" + biometricsId, HttpMethod.GET, httpEntity, EKycVerificationApiResponseDto.class);

            return getResponseTemplate(logManager, log, eKycVerificationApiResponseDtoResponseEntity, loggerCommon);
        }
        catch (Exception e) {
            e.printStackTrace();
            logManager.error(loggerCommon.getLog(log, "error", 1, "Something went wrong on eKYC DeDup by Biometrics ID.", e.getMessage()));
            return ResponseTemplate.error("Something went wrong! Please try again.");
        }
    }

    private static ResponseTemplate<List<String>> getResponseTemplate(LogManager logManager, Log log, ResponseEntity<EKycVerificationApiResponseDto> eKycVerificationApiResponseDtoResponseEntity, LoggerCommon loggerCommon) {
        if (eKycVerificationApiResponseDtoResponseEntity.getStatusCode().is2xxSuccessful() && eKycVerificationApiResponseDtoResponseEntity.getBody().getStatus().equals("200") && eKycVerificationApiResponseDtoResponseEntity.getBody().getDecision()) {
            return ResponseTemplate.success(eKycVerificationApiResponseDtoResponseEntity.getBody().getData());
        }
        else {
            logManager.info(loggerCommon.getLog(log, "info", 1, "Invalid eKyc Biometrics Data!", eKycVerificationApiResponseDtoResponseEntity));
            return ResponseTemplate.error("Invalid eKyc Biometrics Data!");
        }
    }

    public ResponseTemplate<?> eKycVerification(EKycVerificationApiRequestDto request, String biometricsId, LogManager logManager, Log log) {
        log.setTargetSystem("EKYC");
        try {
            RestTemplate restTemplate = new RestTemplate();
            ResponseEntity<Map<String, String>> eKycAuthResponse = eKycAuth(logManager, log);
            if (!eKycAuthResponse.getStatusCode().is2xxSuccessful()) {
                return ResponseTemplate.error(eKycAuthResponse.getBody().get("message"));
            }

            String token = eKycAuthResponse.getBody().get("access_token");

            HttpHeaders headers = new HttpHeaders();
            headers.setBearerAuth(token);

            request.getBiometricData().get(0).setBiometricSubType(eKycFingerType);

            HttpEntity<?> httpEntity = new HttpEntity<>(request, headers);

            ResponseEntity<EKycVerificationIdApiResponseDto> eKycVerificationIdApiResponseDtoResponseEntity = restTemplate.exchange(eKycVerificationUrl + biometricsId, HttpMethod.POST, httpEntity, EKycVerificationIdApiResponseDto.class);

            if (eKycVerificationIdApiResponseDtoResponseEntity.getStatusCode().is2xxSuccessful() && eKycVerificationIdApiResponseDtoResponseEntity.getBody().getStatus()) {
                return ResponseTemplate.success(eKycVerificationIdApiResponseDtoResponseEntity.getBody().getEkycId());
            }
            else {
                logManager.info(loggerCommon.getLog(log, "info", 1, "Biometrics Verification Failed!", eKycVerificationIdApiResponseDtoResponseEntity));
                return ResponseTemplate.error("Biometrics Verification Failed!");
            }
        }
        catch (Exception e) {
            e.printStackTrace();
            logManager.error(loggerCommon.getLog(log, "error", 1, "Something went wrong on eKYC Biometrics Verification.", e.getMessage()));
            return ResponseTemplate.error("Something went wrong! Please try again.");
        }
    }

    public ResponseTemplate<?> eKycVetting(String biometricsId, LogManager logManager, Log log) {
        log.setTargetSystem("EKYC");
        try {
            RestTemplate restTemplate = new RestTemplate();
            ResponseEntity<Map<String, String>> eKycAuthResponse = eKycAuth(logManager, log);
            if (!eKycAuthResponse.getStatusCode().is2xxSuccessful()) {
                return ResponseTemplate.error(eKycAuthResponse.getBody().get("message"));
            }

            String token = eKycAuthResponse.getBody().get("access_token");

            HttpHeaders headers = new HttpHeaders();
            headers.setBearerAuth(token);

            HttpEntity<?> httpEntity = new HttpEntity<>(headers);

            ResponseEntity<Map<String, Object>> eKycVettingResponseEntity = restTemplate.exchange(eKycVettingUrl + "/" + biometricsId, HttpMethod.GET, httpEntity, new ParameterizedTypeReference<>() {
            });

            if (eKycVettingResponseEntity.getStatusCode().is2xxSuccessful() && Boolean.parseBoolean(eKycVettingResponseEntity.getBody().get("vetted").toString())) {
                return ResponseTemplate.success(eKycVettingResponseEntity.getBody().get("ekycId"));
            }
            else {
                logManager.info(loggerCommon.getLog(log, "info", 1, "eKyc Vetting Failed!", eKycVettingResponseEntity));
                return ResponseTemplate.error("eKyc Vetting Failed!");
            }
        }
        catch (Exception e) {
            e.printStackTrace();
            logManager.error(loggerCommon.getLog(log, "error", 1, "Something went wrong on eKYC DeDup by Biometrics ID.", e.getMessage()));
            return ResponseTemplate.error("Something went wrong! Please try again.");
        }
    }

    public ResponseTemplate<?> eKycCreateAgent(CreateAgentApiRequestDto request, LogManager logManager, Log log , String transactionId) {
        log.setTargetSystem("EKYC");
        try {
            RestTemplate restTemplate = new RestTemplate();
            ResponseEntity<Map<String, String>> eKycAuthResponse = eKycAuth(logManager, log);
            if (!eKycAuthResponse.getStatusCode().is2xxSuccessful()) {
                return ResponseTemplate.error(eKycAuthResponse.getBody().get("message"));
            }

            String token = eKycAuthResponse.getBody().get("access_token");

            HttpHeaders headers = new HttpHeaders();
            headers.setBearerAuth(token);

            request.setRoles(List.of(eKycAgentEKycRole, eKycAgentMPESAEKycRole));

            HttpEntity<?> httpEntity = new HttpEntity<>(request, headers);
            logManager.error(loggerCommon.getLog(log, "info",   transactionId ,0, "Request to ekyc!" , request));

            ResponseEntity<CreateAgentApiResponseDto> eKycCreateAgentResponseEntity = restTemplate.exchange(eKycCreateAgentUrl, HttpMethod.POST, httpEntity, CreateAgentApiResponseDto.class);
            logManager.error(loggerCommon.getLog(log, "info",   transactionId ,0, "Response from ekyc!", eKycCreateAgentResponseEntity));

            System.err.println("EKYC Response: " + eKycCreateAgentResponseEntity);
            if (eKycCreateAgentResponseEntity.getStatusCode().is2xxSuccessful() && eKycCreateAgentResponseEntity.getBody().getStatus() && !eKycCreateAgentResponseEntity.getBody().getDescription().toLowerCase().contains("exist")) {
                return ResponseTemplate.success(eKycCreateAgentResponseEntity.getBody());
            }
            else if (eKycCreateAgentResponseEntity.getStatusCode().is2xxSuccessful() &&
                    (eKycCreateAgentResponseEntity.getBody().getDescription() != null && eKycCreateAgentResponseEntity.getBody().getDescription().toLowerCase().contains("exist"))
            ) {
                logManager.error(loggerCommon.getLog(log, "warn",   transactionId ,2, "Creating Agent Failed!", eKycCreateAgentResponseEntity));
              //  return new ResponseTemplate<>(true, "Application Rejected! Because an agent has already been created before!", 2, null);
                return new ResponseTemplate<>(true, "agent has already been created before", 2 , null);
            }
            else {
                logManager.error(loggerCommon.getLog(log, "error",  transactionId , 1, "Creating Agent Failed!", eKycCreateAgentResponseEntity));
                return ResponseTemplate.error("Creating Agent Failed!");
            }
        }
        catch (Exception e) {
            e.printStackTrace();
            logManager.error(loggerCommon.getLog( log,  "error",   transactionId , 1, "Something went wrong on creating an agent.", e.getMessage()));
            return ResponseTemplate.error("Something went wrong! Please try again.");
        }
    }

    public ResponseTemplate<?> eKycUpdateAgent(EKYCUpdateAgentApiRequestDto request, LogManager logManager, Log log) {
        log.setTargetSystem("EKYC");
        try {
            RestTemplate restTemplate = new RestTemplate();
            ResponseEntity<Map<String, String>> eKycAuthResponse = eKycAuth(logManager, log);
            assert eKycAuthResponse.getBody() != null;
            if (!eKycAuthResponse.getStatusCode().is2xxSuccessful()) {
                return ResponseTemplate.error(eKycAuthResponse.getBody().get("message"));
            }

            String token = eKycAuthResponse.getBody().get("access_token");

            HttpHeaders headers = new HttpHeaders();
            headers.setBearerAuth(token);

            request.setRoles(List.of(eKycAgentEKycRole, eKycAgentMPESAEKycRole));

            HttpEntity<?> httpEntity = new HttpEntity<>(request, headers);

            ResponseEntity<CreateAgentApiResponseDto> eKycUpdateAgentResponseEntity = restTemplate.exchange(eKycCreateAgentUrl, HttpMethod.PUT, httpEntity, CreateAgentApiResponseDto.class);

            if (eKycUpdateAgentResponseEntity.getStatusCode().is2xxSuccessful() && Objects.requireNonNull(eKycUpdateAgentResponseEntity.getBody()).getStatus()) {
                return ResponseTemplate.success(eKycUpdateAgentResponseEntity.getBody());
            }
            else {
                logManager.error(loggerCommon.getLog(log, "error", 1, "Updating Agent Failed!", eKycUpdateAgentResponseEntity));
                return ResponseTemplate.error("Updating Agent Failed!");
            }
        }
        catch (Exception e) {
            e.printStackTrace();
            logManager.error(loggerCommon.getLog(log, "error", 1, "Something went wrong on updating an agent.", e.getMessage()));
            return ResponseTemplate.error("Something went wrong! Please try again.");
        }
    }

    public ResponseTemplate<?> dxlEKycCreateAgent(DxLEkycRequestDto request, LogManager logManager, Log log , String transactionId) {
        log.setTargetSystem("DxL");
        try {
            RestTemplate restTemplate = new RestTemplate();
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            request.setCommandID(dxlEycAddAgentCommandId);
            request.setSourceSystem(dxlEycAddAgentSourceSystem);
            request.setVersion(dxlEycAddAgentVersion);
            request.setTimestamp(LocalDateTime.now().toString());

            List<DxLEkycRequestDto.Parameter> modifiableParams = new ArrayList<>(request.getParameters());
            modifiableParams.add(DxLEkycRequestDto.Parameter.builder()
                    .key("roles_after")
                    .value(String.join(",", List.of(eKycAgentEKycRole, eKycAgentMPESAEKycRole)))
                    .build());
            request.setParameters(modifiableParams);

            HttpEntity<?> httpEntity = new HttpEntity<>(request, headers);
            logManager.error(loggerCommon.getLog(log, "info",   transactionId ,0, "Request to dxl!" , request));

            ResponseEntity<Map<String, Object>> eKycCreateAgentResponseEntity = restTemplate.exchange(dxlEycAddAgentUrl, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<>() {
            });   logManager.error(loggerCommon.getLog(log, "info",   transactionId ,0, "Response from ekyc!", eKycCreateAgentResponseEntity));




            if (eKycCreateAgentResponseEntity.getStatusCode().is2xxSuccessful() && eKycCreateAgentResponseEntity.getBody().get("ResponseCode").toString().equals("0")) {
                return ResponseTemplate.success(eKycCreateAgentResponseEntity.getBody());
            }
            else {
                logManager.error(loggerCommon.getLog(log, "error",   transactionId ,1, "Creating Agent Failed!", eKycCreateAgentResponseEntity));
                return ResponseTemplate.error("Creating Agent Failed!");
            }
        }
        catch (Exception e) {
            e.printStackTrace();
            logManager.error(loggerCommon.getLog(log, "error",   transactionId ,1, "Something went wrong on creating an agent.", e.getMessage()));
            return ResponseTemplate.error("Something went wrong! Please try again.");
        }
    }

    private ResponseEntity<Map<String, String>> crmAuth(LogManager logManager, Log log) {
        try {
            RestTemplate restTemplate = new RestTemplate();
            HttpHeaders headers = new HttpHeaders();
            headers.set("x-correlation-conversationid", crmConversationId);
            headers.set("x-source-system", crmSourceSystem);
            headers.set("x-source-identity-token", crmSourceIdentityToken);

            HttpEntity<Map<String, String>> httpEntity = new HttpEntity<>(Map.of(
                    "username", crmAuthUsername,
                    "password", crmAuthPassword
            ), headers);

            ResponseEntity<Map<String, String>> response = restTemplate.exchange(crmAuthUrl, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<>() {
            });

            logManager.info(loggerCommon.getLog(log, "info", 0, "CRM Authentication", response.getBody()));

            return response;
        }
        catch (Exception e) {
            e.printStackTrace();
            logManager.error(loggerCommon.getLog(log, "error", 1, "Something went wrong on CRM Authentication.", e.getMessage()));
            return ResponseEntity.internalServerError().body(Map.of("message", "Something went wrong on CRM Authentication!"));
        }
    }

    public ResponseTemplate<Map<String, String>> crmCustomerInfo(String serviceId, LogManager logManager, Log log) {
        log.setTargetSystem("CRM_CUSTOMER_INFO");
        try {
            RestTemplate restTemplate = new RestTemplate();
            ResponseEntity<Map<String, String>> crmAuthResponse = crmAuth(logManager, log);
            String token = "";
            if (!crmAuthResponse.getStatusCode().is2xxSuccessful()) {
                return ResponseTemplate.error(crmAuthResponse.getBody().get("message"));
            } else if (crmAuthResponse.getBody().containsKey("Status") && crmAuthResponse.getBody().get("Status").equalsIgnoreCase("1000")) {
                token = crmAuthResponse.getBody().get("Token");
            }
            else {
                return ResponseTemplate.error(crmAuthResponse.getBody().get("Desc"));
            }

            HttpHeaders headers = new HttpHeaders();
            headers.setBearerAuth(token);
            headers.set("x-source-system", crmSourceSystem);
            headers.set("x-route-id", crmRouteId);
            headers.set("x-correlation-id", log.getTransactionID());

            HttpEntity<?> httpEntity = new HttpEntity<>(headers);

            ResponseEntity<CRMCustomerInfoApiResponseDto> crmCustomerInfoResponseEntity = restTemplate.exchange(crmCustomerInfoUrl + serviceId, HttpMethod.GET, httpEntity, CRMCustomerInfoApiResponseDto.class);

            if (crmCustomerInfoResponseEntity.getStatusCode().is2xxSuccessful() && crmCustomerInfoResponseEntity.getBody().getCode().equalsIgnoreCase("1000")) {
                Map<String, String> response = new LinkedHashMap<>();
                CRMCustomerInfoApiResponseDto crmCustomerInfoApiResponseDto = crmCustomerInfoResponseEntity.getBody();
//              Note: CRM Customer Status Check - Check which status is valid
                response.put("crmMsisdn", serviceId);
                crmCustomerInfoApiResponseDto.getCustomer().getParts().forEach(part -> {
                    if (part.getName().equalsIgnoreCase("givenName")) {
                        response.put("firstName", part.getValue());
                    }
                    else if (part.getName().equalsIgnoreCase("middleName")) {
                        response.put("middleName", part.getValue());
                    }
                    else if (part.getName().equalsIgnoreCase("familyName")) {
                        response.put("lastName", part.getValue());
                    }
                    else if (part.getName().equalsIgnoreCase("biometricId")) {
                        response.put("crmBiometricId", part.getValue());
                    }
                });

                return ResponseTemplate.success(response);
            }
            else {
                logManager.info(loggerCommon.getLog(log, "info", 1, "Customer Info Failed!", crmCustomerInfoResponseEntity));
                return ResponseTemplate.error("Customer Info Failed!");
            }
        }
        catch (Exception e) {
            e.printStackTrace();
            logManager.error(loggerCommon.getLog(log, "error", 1, "Something went wrong on CRM Customer Info Check.", e.getMessage()));
            return ResponseTemplate.error("Something went wrong! Please try again.");
        }
    }

    public void sendSms(SmsRequestDto smsRequest) {
        try {
            RestTemplate restTemplate = new RestTemplate();
            String basicAuth = "Basic " + Base64.getEncoder().encodeToString((smsUserName + ":" + smsPassword).getBytes());

            HttpHeaders headers = new HttpHeaders();
            headers.set(HttpHeaders.AUTHORIZATION, basicAuth);
            headers.setContentType(MediaType.APPLICATION_JSON);

            smsRequest.setCommandID(smsCommandId);
            smsRequest.setMessageType(smsMessageType);
            smsRequest.setSender(SmsRequestDto.Sender.builder().name(smsSenderName).build());

            HttpEntity<?> requestEntity = new HttpEntity<>(smsRequest, headers);

            ResponseEntity<String> response = restTemplate.postForEntity(dxlSendSMSUrl, requestEntity, String.class);

            System.err.println("SMS Sent: " + response.getBody());
        }
        catch (Exception ex) {
            System.err.println("Unable to send SMS: " + ex.getMessage());
        }
    }

}
