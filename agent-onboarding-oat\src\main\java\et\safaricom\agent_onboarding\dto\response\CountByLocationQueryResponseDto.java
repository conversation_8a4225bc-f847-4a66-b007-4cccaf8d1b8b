package et.safaricom.agent_onboarding.dto.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;

import java.util.Map;

@Data
@ToString
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class CountByLocationQueryResponseDto {
    @JsonProperty("divisionCounts")
    private Map<String, Integer> divisionCounts;

    @JsonProperty("regionCounts")
    private Map<String, Integer> regionCounts;

    @JsonProperty("clusterCounts")
    private Map<String, Integer> clusterCounts;
}
